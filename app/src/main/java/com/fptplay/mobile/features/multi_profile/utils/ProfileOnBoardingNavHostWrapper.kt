package com.fptplay.mobile.features.multi_profile.utils

import android.os.Bundle
import androidx.navigation.NavController
import com.fptplay.mobile.HomeActivity
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.application.FptPlayLifecycleObserver
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.common.utils.PageId
import com.google.android.material.tabs.TabLayout
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import timber.log.Timber

class ProfileOnBoardingNavHostWrapper(
    private val sharedPrefs: SharedPreferences,
    private val activity: HomeActivity?,
    private val currentTabLayout: TabLayout? = null
) {
    private val isSelectionOnBoarding get() = sharedPrefs.getEnableProfileOnLogin() == "1"
    fun switchToOnBoardingNavGraph() {
        if (MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile && isSelectionOnBoarding && sharedPrefs.userLogin()) {
            val navHomeMain = findNavHomeMainController()
            if (navHomeMain != null) {
                navigateToHomeTab(PageId.AppPageId.id, navController = navHomeMain) {
                    navHomeMain.navigate(
                        NavHomeMainDirections.actionGlobalToMultiProfile(
                            isSelectionOnBoarding = true,
                            targetScreen = StartingTargetScreenType.SelectProfile
                        )
                    )
                }
                return
            }
        }
    }

    private fun navigateToHomeTab(
        homeTabId: String?,
        navController: NavController? = null,
        extendsArgs: Bundle? = null,
        block: (() -> Unit)? = null,
    ) {
        try {
            if (currentTabLayout == null) {
                if (navController?.currentDestination?.id == R.id.home_main_fragment) {
                    Timber.d("*****setDefaultTabId: $homeTabId")
                    navController.currentBackStackEntry?.arguments?.putString(
                        "defaultTabId",
                        homeTabId
                    )
                    navController.currentBackStackEntry?.arguments?.putBundle(
                        DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                        extendsArgs
                    )
                } else {
                    navController?.navigateSafe(
                        directions = NavHomeMainDirections.actionGlobalToHomeMainFragment(
                            defaultTabId = homeTabId,
                            navigateToVodPage = false
                        ),
                        extendArgs = if (extendsArgs != null) {
                            Bundle().apply {
                                putBundle(
                                    DeeplinkConstants.DEEPLINK__PAGE_IZIOS__EXTEND_ARGS__BUNDLE_NAME,
                                    extendsArgs
                                )
                            }
                        } else {
                            null
                        }
                    )
                }
            } else {
                var isMenu = false
                for (index in 0..currentTabLayout.tabCount) {
                    if (currentTabLayout.getTabAt(index)?.tag == homeTabId) {
                        currentTabLayout.getTabAt(index)?.select()
                        isMenu = true
                        break
                    }
                }
                if (!isMenu) {
                    if (homeTabId != null) {
                        Timber.w("setDefaultTabMenu select first page")
                        try {
                            val currentPosition =
                                if (currentTabLayout.selectedTabPosition in 0 until currentTabLayout.tabCount)
                                    currentTabLayout.selectedTabPosition
                                else
                                    0
                            currentTabLayout.getTabAt(currentPosition)?.select()
                        } catch (ex: Exception) {
                            ex.printStackTrace()
                        }
                    }
                }
            }
        } finally {
            block?.invoke()
        }
    }

    private fun findNavHomeMainController(): NavController? {
        if (activity == null) {
            return null
        }
        val navController = activity.navHostFragment?.navController
        return if (navController?.graph?.id == R.id.nav_home_main) {
            navController
        } else {
            null
        }
    }
}