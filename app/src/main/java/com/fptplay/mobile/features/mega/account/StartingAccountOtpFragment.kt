package com.fptplay.mobile.features.mega.account

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.NavAccountOtpActionDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.databinding.StartingFragmentBinding
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.account.model.AccountOtpTargetScreen.*
import com.fptplay.mobile.features.mega.account.model.AccountOtpType
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class StartingAccountOtpFragment :
    BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>() {
    //    companion object{
//        sealed class TypeScreen {
//            object VerifyOTPChangePassword : TypeScreen()
//            object DeleteAccount : TypeScreen()
//        }
//    }
    override val viewModel by activityViewModels<MegaViewModel>()
    private var _binding: StartingFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: StartingAccountOtpFragmentArgs by navArgs()

    @Inject
    lateinit var sharedPreferences: SharedPreferences
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = StartingFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun MegaViewModel.MegaState.toUI() {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun bindComponent() {
        when (safeArgs.targetScreen) {
            VerifyOtpChangePassword -> {
                findNavController().navigate(
                    NavAccountOtpActionDirections.actionGlobalAccountToVerityAccountOtpV2Fragment(
                        otpType = AccountOtpType.VerifyOtpChangePassword,
                        verifyToken = safeArgs.verifyToken,
                        popupToId = R.id.starting_account_otp_fragment,
                        popupToInclusive = true
                    )
                )
            }

            DeleteAccount -> {
                if (safeArgs.isPackage) {
                    findNavController().navigate(NavAccountOtpActionDirections.actionGlobalToDeleteAccountPackageUserFragment())
                } else {
                    findNavController().navigate(NavAccountOtpActionDirections.actionGlobalToDeleteAccountPolicyFragment())
                }
            }

            CreatePinSetting -> {
                findNavController().navigate(NavAccountOtpActionDirections.actionGlobalToAccountChangePinSettingFragment(
                    createPinSetting = true,
                    verifyToken = "",
                    popupToId = R.id.starting_account_otp_fragment,
                    popupToInclusive = true
                ))
            }

            VerifySaleMode -> {
                findNavController().navigate(
                    NavAccountOtpActionDirections.actionGlobalAccountToVerityAccountOtpV2Fragment(
                        otpType = AccountOtpType.VerifySaleMode,
                        verifyToken = safeArgs.verifyToken,
                        email = safeArgs.email,
                        popupToId = R.id.starting_account_otp_fragment,
                        popupToInclusive = true
                    )
                )
            }
//            else -> {
//                //other type screen
//            }
        }
    }
}