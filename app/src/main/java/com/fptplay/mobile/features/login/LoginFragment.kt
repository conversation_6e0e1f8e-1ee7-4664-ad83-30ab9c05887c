package com.fptplay.mobile.features.login

import android.content.Context
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.ImageView
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.getDisplayHeight
import com.fptplay.mobile.common.extensions.getDisplayWidth
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.global.GlobalEvent
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.GlideApp
import com.fptplay.mobile.common.utils.StartCropTransformation
import com.fptplay.mobile.common.utils.TopCropTransformation
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.LoginFragmentBinding
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.home.HomeViewModel
import com.fptplay.mobile.features.mega.account.AccountChangePasswordFragmentDirections
import com.fptplay.mobile.features.mega.util.CheckNavigateMegaUtils
import com.fptplay.mobile.features.mqtt.MqttConnectManager
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.multi_profile.utils.StartingTargetScreenType
import com.fptplay.mobile.features.pladio.util.context
import com.fptplay.mobile.features.tracking_appsflyer.TrackingAppsFlyerProxy
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.image.ImageProxy
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.min

@AndroidEntryPoint
class LoginFragment : BaseFragment<LoginViewModelV2.LoginState, LoginViewModelV2.LoginIntent>() {

    //region object
    companion object {
        const val LOGIN_RESULT_REQUEST_KEY = "requestKey"
        const val LOGIN_RESULT_BUNDLE_KEY = "bundleKey"
    }

    //endregion object
    override val viewModel: LoginViewModelV2 by activityViewModels()
    override val hasEdgeToEdge = true
    private val safeArgs: LoginFragmentArgs by navArgs()
    private var _binding: LoginFragmentBinding? = null
    private val binding get() = _binding!!
    private var isLoginSuccess = false
    private var lastStatusLogin = false
    override val handleConfigurationChange = true
    private val isSelectionOnBoarding get() = sharedPreferences.getEnableProfileOnLogin() == "1"
    @Inject
    lateinit var sharedPreferences: SharedPreferences


    private var oldProfileType = ""
    private var oldProfileId = ""
    private var isLoginProfileChanged = false
    private val isOpenSelectOnBoarding get() =!(MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile && isSelectionOnBoarding && sharedPreferences.userLogin())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setFragmentResult(Constants.LOGIN_SHOW, bundleOf(Constants.LOGIN_SHOW to true))
        //MQTT
        MqttConnectManager.INSTANCE.unSubscriberToUserTopic()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = LoginFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        if (!sharedPreferences.userLogin() || isLoginProfileChanged || isOpenSelectOnBoarding) {
            sendEventLastStatusLogin()
        }
        super.onDestroyView()
        _binding = null
    }

    override fun bindData() {
        if(sharedPreferences.isEnableSalesMode()) {
            showRestartAppDialog()
        }

        isLoginProfileChanged = arguments?.getBoolean("isLoginProfileChanged") ?: false
        lastStatusLogin = sharedPreferences.userLogin()
        oldProfileType = sharedPreferences.profileType()
        oldProfileId = sharedPreferences.profileId()
        Timber.tag("tam-multiProfile").w("oldProfileType - oldProfileId in ${this.javaClass.simpleName} : $oldProfileType - $oldProfileId")
        viewModel.saveStartScreenData(safeArgs, arguments)
        viewModel.dispatchIntent(LoginViewModelV2.LoginIntent.AddDeviceRegistrationToken)

        if(!isLoginProfileChanged){
            Utils.clearUserData(sharedPreferences)
        }
    }
    override fun bindComponent() {
        changeBg(MainApplication.INSTANCE.applicationContext.resources.configuration.orientation)
    }
    override fun bindOrientationStateChange(newConfig: Configuration) {
        Logger.d("------change orientation in login isLandscape = ${newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE}")
        changeBg(newConfig.orientation)
    }

    private fun changeBg(currentOrientation:Int) {
        var url = MainApplication.INSTANCE.appConfig.loginBg
        var drawableError = R.drawable.bg_login_local_mobile
        if (context.isTablet()) {
            if (currentOrientation == Configuration.ORIENTATION_LANDSCAPE) {
                url = MainApplication.INSTANCE.appConfig.loginBgTablet
                drawableError = R.drawable.bg_login_local_tablet
                setImageBackgroundForLandscape(url, drawableError)
            } else if (currentOrientation == Configuration.ORIENTATION_PORTRAIT) {
                url = MainApplication.INSTANCE.appConfig.loginBg
                drawableError = R.drawable.bg_login_local_mobile
                setImageBackgroundForPortrait(url, drawableError)
            } else if((context?.getDisplayWidth()?: 0) < (context?.getDisplayHeight()?: 0)){
                url = MainApplication.INSTANCE.appConfig.loginBg
                drawableError = R.drawable.bg_login_local_mobile
                setImageBackgroundForPortrait(url, drawableError)
            } else {
                url = MainApplication.INSTANCE.appConfig.loginBgTablet
                drawableError = R.drawable.bg_login_local_tablet
                setImageBackgroundForLandscape(url, drawableError)
            }
        } else {
            url = MainApplication.INSTANCE.appConfig.loginBg
            drawableError = R.drawable.login_bg
            setImageBackgroundForPortrait(url, drawableError)
        }
//        binding.ivBg.setImageResource(R.drawable.login_bg_tablet)
        Logger.d("======= mode: $currentOrientation -url: $url")
    }

    fun setImageBackgroundForLandscape(imageUrl: String, errorDrawableId: Int) {
        Logger.d("======= setImageBackgroundForLandscape")
        val width = max(
            (context?.getDisplayWidth() ?: sharedPreferences.getDisplayWidth()),
            (context?.getDisplayHeight() ?: sharedPreferences.getDisplayHeight())
        )
        val height = min(
            (context?.getDisplayWidth() ?: sharedPreferences.getDisplayWidth()),
            (context?.getDisplayHeight() ?: sharedPreferences.getDisplayHeight())
        )

        if (imageUrl.isBlank()) {
            binding.ivBg.setImageResource(errorDrawableId)
        } else {
            Logger.d("======= load url: $imageUrl")

            GlideApp.with(this)
                .load(ImageProxy.optimizeImageUrl(imageUrl, width, height))
                .error(errorDrawableId)
                .transform(StartCropTransformation(screenWidth = width, screenHeight = height))
                .into(binding.ivBg)
        }

        binding.ivBg.apply {
            scaleType = ImageView.ScaleType.MATRIX
            invalidate()
            requestLayout()
        }
    }

    fun setImageBackgroundForPortrait(imageUrl: String, errorDrawableId: Int) {
        Logger.d("======= setImageBackgroundForPortrait")

        if (imageUrl.isBlank()) {
            binding.ivBg.setImageResource(errorDrawableId)
        } else {
            Logger.d("======= load url: $imageUrl")
            val width = min(
                (context?.getDisplayWidth() ?: sharedPreferences.getDisplayWidth()),
                (context?.getDisplayHeight() ?: sharedPreferences.getDisplayHeight())
            )
            val height = max(
                (context?.getDisplayWidth() ?: sharedPreferences.getDisplayWidth()),
                (context?.getDisplayHeight() ?: sharedPreferences.getDisplayHeight())
            )
            GlideApp.with(this)
                .load(ImageProxy.optimizeImageUrl(imageUrl, width, height))
                .error(errorDrawableId)
                .transform(TopCropTransformation( screenWidth = width, screenHeight = height))
                .into(binding.ivBg)
        }

        binding.ivBg.apply {
            scaleType = ImageView.ScaleType.MATRIX
            invalidate()
            requestLayout()
        }
    }

    override fun bindEvent() {
        binding.ivBg.setOnClickListener {
            val imm = activity?.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(binding.ivBg.windowToken, 0)
        }
        childFragmentManager
            .findFragmentById(R.id.nav_host_fragment)
            ?.childFragmentManager
            ?.setFragmentResultListener(LOGIN_RESULT_REQUEST_KEY, this) { _, bundle ->
                if (bundle.getBoolean(LOGIN_RESULT_BUNDLE_KEY)) {
                    TrackingAppsFlyerProxy.sendTrackingAppsFlyerLoginSuccess()
                    isLoginSuccess = true
                    val args = Bundle()
                    args.apply {
                        if (viewModel.navigationId()!=-1){
                            putInt(Constants.NAVIGATION_ID, viewModel.navigationId())
                        }
                        putInt(Constants.PREVIOUS_BACK_STACK_ENTRY_ID, findNavController().previousBackStackEntry?.destination?.parent?.id?:-1)
                        putBoolean(Constants.IS_SELECTION_ON_BOARDING, true)
                        putBoolean(Constants.IS_FROM_LOGIN_SCREEN, true)
                        putBoolean(Constants.POP_UP_TO_INCLUSIVE, viewModel.popUpToInclusive())
                        putBoolean(Constants.REQUEST_RESTART_APP, viewModel.requestRestartApp())
                    }
                    if (viewModel.navigationId() == R.id.nav_multi_profile && viewModel.requestFromOnBoarding()) {
                        // case: start app -> onboarding -> switch profile -> login -> back to onboarding (prevent to duplicate on boarding when remove devices)
                        // note: mega not support logic it
                        setFragmentResult(
                            Utils.PROFILE_REFRESH_LIST_PROFILE_EVENT,
                            bundleOf(Utils.PROFILE_HAVE_CHANGE_DATA to true)
                        )
                        findNavController().navigateUp()
                    }
                    else {
                        if (MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile && MultiProfileUtils.shouldShowProfileSelection(sharedPreferences)){
                            // check case : after user login check cờ show onboarding from api
                            args.putSerializable("targetScreen", StartingTargetScreenType.SelectProfile)
                            findNavController().navigate(
                                R.id.nav_multi_profile, args, NavOptions.Builder().build()
                            )
                        }
                        else{
                            onNavigateAfterSwitchProfile()
                        }
                    }

                    //MQTT
                    MqttConnectManager.INSTANCE.subscriptionToUserTopic()

                } else {
                    isLoginSuccess = false
                }
            }
        setFragmentResultListener(Utils.PROFILE_ONBOARD_SWITCH_PROFILE_CHANGED_EVENT) { _, bundle ->
            if (findNavController().previousBackStackEntry?.destination?.parent?.id == R.id.nav_multi_profile){
                MultiProfileUtils.restartHome(activity)
            }
            else{
                onNavigateAfterSwitchProfile()
            }
        }
    }

    fun navigateBack() {
        if (viewModel.popUpToId() == -1) {
            findNavController().navigateUp()
        } else {
            findNavController().navigateUp()
            val navOptions = NavOptions.Builder()
                .setPopUpTo(viewModel.popUpToId(), viewModel.popUpToInclusive())
                .build()
            val args = viewModel.extraData() ?: Bundle()
            args.apply {
                putBoolean("use_args", false)

            }
            findNavController().navigate(viewModel.popUpToId(), args, navOptions)
        }
    }

    private fun showRestartAppDialog() {
        showWarningDialog(
            isShowTitle = true,
            textTitle = "Đăng nhập tài khoản",
            message = "Để đảm bảo không bị gián đoạn trải nghiệm, FPT Play sẽ khởi động app trước khi thực hiện đăng nhập.",
            textConfirm = binding.context.getString(R.string.close),
            isOnlyConfirmButton = true,
            onConfirm = {
                Utils.clearUserData(sharedPreferences = sharedPreferences, clearSaleMode = true)
                Utils.restartApp(requireContext())
            }
        )
    }

    private fun sendEventLastStatusLogin(){
        val bundle: Bundle = bundleOf(Constants.LOGIN_SUCCESS_KEY to isLoginSuccess)
        bundle.putBoolean(Constants.HAVE_CHANGE_DATA, lastStatusLogin || sharedPreferences.userLogin())
        bundle.putBundle(Constants.EXTENDS_ARG_NAVIGATE_LOGIN_KEY, viewModel.extraData() ?: Bundle())
        Logger.d("trangtest lastStatusLogin = $lastStatusLogin")
        Logger.d("trangtest sharedPreferences.userLogin() = ${sharedPreferences.userLogin()}")
        setFragmentResult(Constants.LOGIN_SUCCESS, bundle)
        setFragmentResult(Constants.LOGIN_SUCCESS_FOR_DIALOG, bundle)
        setFragmentResult(Constants.LOGIN_SHOW, bundleOf(Constants.LOGIN_SHOW to false))
        MultiProfileUtils.sendEventProfileChangedIfChange(
            fragment = this,
            sharedPreferences = sharedPreferences,
            sourceChange = "onDestroy LoginFragment",
            oldProfileId = oldProfileId,
            oldProfileType = oldProfileType
        )
        Timber.tag("tam-multiProfile").w("newProfileType - newProfileId in ${this.javaClass.simpleName} : ${sharedPreferences.profileType()} - ${sharedPreferences.profileId()}")
        // Global Event
        GlobalEvent.pushEvent(GlobalEvent.LOGIN_EVENT, isLoginSuccess)
        //
    }

    private fun onNavigateAfterSwitchProfile(){
        MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile = false // not show onboard switches profile again
        when {
            viewModel.navigationId() != -1 -> {
                if (viewModel.checkRequireVip()) {
                    findNavController().navigateUp()
                    setFragmentResult(Constants.CHECK_REQUIRE_VIP, bundleOf("id" to viewModel.idToPlay()))
                } else {
                    if (viewModel.requestRestartApp()) {
                        MultiProfileUtils.restartHome(activity)
                    } else {
                        findNavController().navigateUp()
                        if(viewModel.navigationId() == DeeplinkUtils.NAVIGATION_ID_DEEP_LINK_HANDLE) {
                           viewModel
                               .extraData()
                               ?.getString(DeeplinkUtils.NAVIGATION_LINK_DEEP_LINK_KEY)?.let {
                                   DeeplinkUtils.parseDeepLinkAndExecute(it)
                               }
                            return
                        }
                        if ((viewModel.navigationId() != R.id.nav_pladio_outer)) {
                            val args = viewModel.extraData() ?: Bundle()
                            args.apply {
                                putString("idToPlay", viewModel.idToPlay())
                                putString("id", viewModel.idToPlay())
                                putInt("time_shift_limit", viewModel.timeShiftLimit())
                                putInt("time_shift", viewModel.timeShift())
                                putBoolean("use_args", true)
                                putBoolean("isPlaylist", viewModel.isPlaylist())
                                putBundle("loginNavigateExtraData", viewModel.extraData())
                            }
                            val navOptions = NavOptions.Builder()
                                .setPopUpTo(viewModel.popUpToId(), viewModel.popUpToInclusive())
                                .build()
                            findNavController().navigate(viewModel.navigationId(), args, navOptions)
                        }
                    }
                }
            }
            else -> {
                if (viewModel.popUpToId() == -1) {
                    if (viewModel.requestRestartApp()) {
                        MultiProfileUtils.restartHome(activity)
                    } else {
                        findNavController().navigateUp()
                    }
                } else {
                    findNavController().navigateUp()
                    if (viewModel.checkRequireVip()) {
                        setFragmentResult(Constants.CHECK_REQUIRE_VIP, bundleOf("id" to viewModel.idToPlay()))
                    }
                    val navOptions = NavOptions.Builder()
                        .setPopUpTo(viewModel.popUpToId(), viewModel.popUpToInclusive())
                        .build()
                    val args = viewModel.extraData() ?: Bundle()
                    args.apply {
                        putString("idToPlay", viewModel.idToPlay())
                        putString("id", viewModel.idToPlay())
                        putInt("time_shift_limit", viewModel.timeShiftLimit())
                        putInt("time_shift", viewModel.timeShift())
                        putBoolean("use_args", true)
                        putBoolean("is_check_required_vip", viewModel.checkRequireVip())
                        putBundle("loginNavigateExtraData",viewModel.extraData())
                    }

                    findNavController().navigate(viewModel.popUpToId(), args, navOptions)
                }
            }
        }
    }
    override fun LoginViewModelV2.LoginState.toUI() {
        when (this) {
            is LoginViewModelV2.LoginState.ResultUserInfo -> {
                AdjustAllEvent.sendAuthSuccessEvent()
                isLoginSuccess = true
            }
            else -> {}
        }
    }
}