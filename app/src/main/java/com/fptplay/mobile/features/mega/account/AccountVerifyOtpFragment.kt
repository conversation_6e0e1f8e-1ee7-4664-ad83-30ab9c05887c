package com.fptplay.mobile.features.mega.account

import android.annotation.SuppressLint
import android.content.IntentFilter
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.os.CountDownTimer
import android.text.InputFilter
import android.text.InputType
import android.text.Spannable
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.NavAccountInfoDirections
import com.fptplay.mobile.NavAccountOtpActionDirections
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.NavControllerExtensions.navigateSafe
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.common.utils.ZendeskUtils
import com.fptplay.mobile.databinding.AccountVerifyOtpFragmentBinding
import com.fptplay.mobile.features.ads.utils.AdsUtils
import com.fptplay.mobile.features.login.gmsotp.SMSReceiver
import com.fptplay.mobile.features.login.utils.formatToInt
import com.fptplay.mobile.features.login.utils.formatToLong
import com.fptplay.mobile.features.mega.MegaViewModel
import com.fptplay.mobile.features.mega.account.model.AccountOtpType.*
import com.fptplay.mobile.player.utils.PlayerPiPHelper
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.domain.entity.fplay.otp.ValidateDisableUserData
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AccountVerifyOtpFragment : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>(),SMSReceiver.OTPReceiveListener {
    companion object {
        const val IS_NOT_ERROR = "0"
        const val IS_SUCCESS = "1"
        const val ERROR_LIMIT_CALL_API = "2"
        const val ERROR_TOKEN_EXPIRE = "9"
    }
    override val viewModel by activityViewModels<MegaViewModel>()
    private val requiredOtpLength by lazy { 6 }
    private val numberCountDownTimer by lazy {30 }
    private var smsReceiver: SMSReceiver? = null
    override val handleBackPressed = true
    private var countDownTimer: CountDownTimer? = null
    private var _binding: AccountVerifyOtpFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: AccountVerifyOtpFragmentArgs  by navArgs()
    private val userDeleteData: ValidateDisableUserData? get() = viewModel.getUserDeleteDataV2()
    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    private var otpLength = requiredOtpLength

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountVerifyOtpFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        countDownTimer?.cancel()
        smsReceiver?.apply {
            activity?.unregisterReceiver(smsReceiver)
            smsReceiver = null
        }
        _binding = null
    }

    override fun bindData() {
        when(safeArgs.otpType) {
            VerifyOtpChangePassword -> {
//                viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestChangePasswordOtp)
                viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestOtpV2(
                    verifyToken = safeArgs.verifyToken ?: "",
                    otpType = UserOtpType.LoginChangePass
                ))
            }
            VerifyOtpDeleteAccount -> {
                viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestOtpV2(
                    verifyToken = safeArgs.verifyToken ?: "",
                    otpType = UserOtpType.DisableAccount
                ))
            }

            VerifySaleMode -> {
                viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestOtpV2(
                    verifyToken = safeArgs.verifyToken ?: "",
                    otpType = UserOtpType.OtpEmail,
                    email = safeArgs.email
                ))
            }
        }
    }
    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            if(safeArgs.popupToId != -1) {
                findNavController().popBackStack(safeArgs.popupToId, safeArgs.popupToInclusive)
            } else {
                findNavController().navigateUp()

            }
        }
        binding.tvResend.onClickDelay {
            when(safeArgs.otpType) {
                VerifyOtpChangePassword -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestResendOtpV2(
                    verifyToken = safeArgs.verifyToken ?: "",
                    otpType = UserOtpType.LoginChangePass
                ))
                VerifyOtpDeleteAccount -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestResendOtpV2(
                    verifyToken = safeArgs.verifyToken ?: "",
                    otpType = UserOtpType.DisableAccount
                ))
                VerifySaleMode -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.RequestResendOtpV2(
                    verifyToken = safeArgs.verifyToken ?: "",
                    otpType = UserOtpType.OtpEmail,
                    email = safeArgs.email,
                ))
            }
        }
        binding.edtInputOtp.doAfterTextChanged {
            verifyOtp()
        }
        binding.edtInputOtp.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE) verifyOtp()
            false
        }
        //set click on background to dismiss keyboard
        binding.root.onClickDelay {
            hideKeyboard()
        }

    }

    private fun verifyOtp(){
        setLayoutTextErrorLine(hasError = false)
        val otp = binding.edtInputOtp.text?.toString()
        if (otp == null || otp.length != requiredOtpLength) return
        activity?.currentFocus?.hideKeyboard()
        when(safeArgs.otpType) {
//            VerifyOtpChangePassword -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.VerifyChangePassOtp(otpCode = otp))
            VerifyOtpChangePassword -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.VerifyOtpV2(otpCode = otp, otpType = UserOtpType.LoginChangePass))

            VerifyOtpDeleteAccount -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.VerifyOtpV2(otpCode = otp, otpType = UserOtpType.DisableAccount))

            VerifySaleMode -> viewModel.dispatchIntent(MegaViewModel.MegaIntent.VerifyOtpV2(otpCode = otp, otpType = UserOtpType.OtpEmail, email = safeArgs.email))

        }
    }
    override fun bindComponent() {
        binding.apply {
            val enterOTPSubtitle =
                if (safeArgs.otpType == VerifySaleMode) {
                    getString(R.string.delete_account_input_email_des, safeArgs.email)
                } else {
                    getString(
                        R.string.delete_account_input_otp_des,
                        replacePhoneNumberParts(viewModel.userPhone())
                    )
                }
            Utils.fromHtml(binding.tvInputOtpSubtitle, enterOTPSubtitle)
            val lengthFilter = InputFilter.LengthFilter(otpLength)
            edtInputOtp.filters = arrayOf(lengthFilter)
            edtInputOtp.inputType =  InputType.TYPE_CLASS_NUMBER
        }
    }

    override fun backHandler() {
        if(safeArgs.popupToId != -1) {
            findNavController().popBackStack(safeArgs.popupToId, safeArgs.popupToInclusive)
        } else {
            findNavController().navigateUp()

        }
    }

    private fun updateAccountToSaleMode(verifyToken: String){
        viewModel.dispatchIntent(MegaViewModel.MegaIntent.UpdateAccountToSaleMode(
            verifyToken = verifyToken
        ))
    }

    override fun MegaViewModel.MegaState.toUI() {
        when (this) {
            is MegaViewModel.MegaState.Loading -> showLoading()

            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                when(intent){
                    is MegaViewModel.MegaIntent.DeleteAccount,
                    is MegaViewModel.MegaIntent.DeleteAccountV2->{
                        logOut(isRequiredLogin = true)
                        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(NavHomeMainDirections.actionGlobalToLogin())
                    }
                    is MegaViewModel.MegaIntent.VerifyOtpV2,
                    is MegaViewModel.MegaIntent.RequestOtpV2,
                    is MegaViewModel.MegaIntent.RequestResendOtpV2->{
                        navigateToLoginWithParams(
                            isDirect = false,
                            requestRestartApp = true
                        )
                    }
                    else ->{

                    }
                }
            }
            is MegaViewModel.MegaState.ResultRequestOtp -> {
                startCountdown(if(data.seconds < numberCountDownTimer) numberCountDownTimer else data.seconds)
            }
            is MegaViewModel.MegaState.ResultVerifyOtp -> {
                when(safeArgs.otpType){
                    VerifyOtpDeleteAccount -> {
                        if(data.errorCode == 0) {
                            setLayoutTextErrorLine(hasError = false)
                            viewModel.dispatchIntent(MegaViewModel.MegaIntent.DeleteAccount(verityToken = data.verifyToken))
                        } else {
                            setLayoutTextErrorLine(hasError = true, messageError = data.message)
                        }
                    }
                    VerifyOtpChangePassword -> {
                        if(data.errorCode == 0) {
                            setLayoutTextErrorLine(hasError = false)
                            findNavController().navigate(
                                NavAccountOtpActionDirections.actionGlobalToAccountChangePinSettingFragment(
                                    createPinSetting = false,
                                    verifyToken = data.verifyToken,
                                    popupToId = if(safeArgs.popupToId != 1)
                                        safeArgs.popupToId
                                    else R.id.starting_account_otp_fragment,
                                    popupToInclusive = safeArgs.popupToInclusive

                                )
                            )
                        } else {
                            setLayoutTextErrorLine(hasError = true, messageError = data.message.ifBlank { data.data?.message?: "" } )
//                            showWarningDialog(
//                                message = data.message.ifBlank { data.data?.message?: "" }
//                            )
                        }
                    }

                    VerifySaleMode -> {
                        if(data.errorCode == 0) {
                            setLayoutTextErrorLine(hasError = false)
                            updateAccountToSaleMode(verifyToken = this.data.verifyToken)
                        } else {
                            setLayoutTextErrorLine(hasError = true, messageError = data.message.ifBlank { data.data?.message?: "" } )
//                            showWarningDialog(
//                                message = data.message.ifBlank { data.data?.message?: "" }
//                            )
                        }
                    }
                }
            }
            is MegaViewModel.MegaState.ResultDeleteAccount ->{
                hideLoading()
                if(status ==200){
                    logOut(msgSuccess = message)
                }
                else{
                    showWarningDeleteAccountFail(message = message)
                }
            }
            is MegaViewModel.MegaState.Error->{
                hideLoading()
                when (intent) {
                    is MegaViewModel.MegaIntent.DeleteAccount,
                    is MegaViewModel.MegaIntent.DeleteAccountV2 -> {
                        showWarningDeleteAccountFail(message = message)
                    }

                    is MegaViewModel.MegaIntent.VerifyDeleteAccountOtp,
                    is MegaViewModel.MegaIntent.VerifyOtpV2 -> {
                        setLayoutTextErrorLine(hasError = true, messageError = message)
                    }

                    is MegaViewModel.MegaIntent.RequestOtpV2,
                    is MegaViewModel.MegaIntent.RequestResendOtpV2 -> {
                        handleErrorRequestOtp(message)
                    }

                    is MegaViewModel.MegaIntent.UpdateAccountToSaleMode -> {
                        showWarningDialog(
                            textTitle = getString(R.string.account_enable_sale_mode_failed_title),
                            message = message,
                            textConfirm = getString(R.string.close),
                            isShowTitle = true,
                            isOnlyConfirmButton = true,
                            onConfirm = {
                                if (safeArgs.popupToId != -1) {
                                    findNavController().popBackStack(
                                        safeArgs.popupToId,
                                        safeArgs.popupToInclusive
                                    )
                                } else {
                                    findNavController().navigateUp()
                                }
                            })
                    }

                    else -> {

                    }
                }
            }
            is MegaViewModel.MegaState.ErrorNoInternet-> {
                hideLoading()
                when (intent) {
                    is MegaViewModel.MegaIntent.RequestOtpV2,
                    is MegaViewModel.MegaIntent.RequestResendOtpV2 -> {
//                        showSnackbar(getString(R.string.new_otp_error_no_internet_text))
                        showWarningDialog(message = message, textConfirm = getString(R.string.alert_close))

                    }
                    else -> {
                        showWarningDialog(message = message)
                    }
                }
            }
            is MegaViewModel.MegaState.Done->{
                hideLoading()
            }
            is MegaViewModel.MegaState.ManyRequest -> {
                hideLoading()
                when (data) {
                    is MegaViewModel.MegaIntent.VerifyOtpV2-> {
                        Timber.tag("tam-fid").e("ManyRequest $this")
                        handleManyRequestVerifyOTP(message)
                    }
                    is MegaViewModel.MegaIntent.RequestOtpV2,
                    is MegaViewModel.MegaIntent.RequestResendOtpV2-> {
                        handleManyRequestSendOtp(seconds = seconds.toString(), message = message)
                    }
                    else -> {

                    }
                }
            }
            is MegaViewModel.MegaState.ResultRequestOtpV2 -> {
                hideLoading()
                if (data.status == IS_SUCCESS) {
                    startSMSListener()
                    binding.tvInputOtpSubtitle.text = boldTextFormatSpannableString(des = data.message, textFormat = data.textFormat)
                    otpLength = data.otpLength.toInt()
                    //set max length for otp
                    val lengthFilter = InputFilter.LengthFilter(otpLength)
                    binding.edtInputOtp.filters = arrayOf(lengthFilter)
                    val time = data.seconds.formatToLong()
                    if (time <= 0L) {
                        enableResendButton()
                    } else {
                        startCountdown(data.seconds.formatToInt())
//                        countdownTimeResend(data.seconds)
                    }
                } else {
                    when (data.errorCode) {
                        ERROR_LIMIT_CALL_API -> {
                            handleManyRequestSendOtp(seconds = data.seconds, message = data.message)
                        }

                        ERROR_TOKEN_EXPIRE -> {
                            showPopUpTokenExpire(data.title, data.message)
                        }

                        else -> {
                            handleErrorRequestOtp(data.message)
                        }
                    }
                }
            }
            is MegaViewModel.MegaState.ResultVerifyOtpV2 ->{
                hideLoading()
                when(safeArgs.otpType){
                    VerifyOtpDeleteAccount -> {
                        if(data.status == IS_SUCCESS && data.errorCode == IS_NOT_ERROR) {
                            setLayoutTextErrorLine(hasError = false)
                            viewModel.dispatchIntent(MegaViewModel.MegaIntent.DeleteAccountV2(verityToken = data.verifyToken))
                        } else {
                            setLayoutTextErrorLine(hasError = true, messageError = data.message)
                        }
                    }
                    VerifyOtpChangePassword -> {
                        if(data.status == IS_SUCCESS && data.errorCode == IS_NOT_ERROR) {
                            setLayoutTextErrorLine(hasError = false)
                            findNavController().navigate(
                                NavAccountOtpActionDirections.actionGlobalToAccountChangePinSettingFragment(
                                    createPinSetting = false,
                                    verifyToken = data.verifyToken,
                                    popupToId = if(safeArgs.popupToId != 1)
                                        safeArgs.popupToId
                                    else R.id.starting_account_otp_fragment,
                                    popupToInclusive = safeArgs.popupToInclusive

                                )
                            )
                        } else {
                            setLayoutTextErrorLine(hasError = true, messageError =data.message)
                        }
                    }

                    VerifySaleMode -> {
                        if(data.status == IS_SUCCESS && data.errorCode == IS_NOT_ERROR) {
                            setLayoutTextErrorLine(hasError = false)
                            updateAccountToSaleMode(verifyToken = this.data.verifyToken)

                        } else {
                            setLayoutTextErrorLine(hasError = true, messageError =data.message)
                        }
                    }
                }
            }
            is MegaViewModel.MegaState.ResultDeleteAccountV2 ->{
                hideLoading()
                if(status == IS_SUCCESS && data.errorCode == IS_NOT_ERROR){
                    logOut(msgSuccess = message)
                }
                else{

                    showWarningDeleteAccountFail(message = message)
                }
            }

            is MegaViewModel.MegaState.ResultUpdateAccountToSaleMode -> {
                hideLoading()
                Utils.restartApp(requireContext())
            }
            else -> {}
        }
    }
    private fun boldTextFormatSpannableString(
        des: String,
        textFormat: List<String>
    ): SpannableString {
        if (textFormat.isEmpty()) return SpannableString(des)
        var spannableString = SpannableString(des)
        for (textFormatItems in textFormat) {
            spannableString = boldTextFormatItemsSpannableString(des = spannableString.toString(), format = textFormatItems)
        }
        return spannableString
    }
    private fun boldTextFormatItemsSpannableString(des: String, format:String): SpannableString {
        val spannableString = SpannableString(des)
        val start = des.indexOf(format)
        val end = start + format.length
        spannableString.setSpan(
            StyleSpan(Typeface.BOLD),
            start,
            end,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spannableString.setSpan(
            ForegroundColorSpan(Color.WHITE),
            start,
            end,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        return spannableString
    }

    private fun showPopUpTokenExpire(title: String, description: String){
        showWarningMessageDialog(
            title.ifBlank { getString(R.string.login_title_token_expire)},
            textConfirm = getString(R.string.close),
            message =  description.ifBlank { getString(R.string.login_des_token_expire) },
            isCancelled = true,
            onConfirm = {
                navigateToAccountInfo()
            })
    }

    private fun enableResendButton(){
        binding.apply {
            tvResend.text = getString(R.string.resend_otp)
            tvResend.isEnabled = true
        }
    }
    private fun enableSendButton(){
        binding.apply {
            tvResend.text = getString(R.string.send_otp)
            tvResend.isEnabled = true
        }
    }

    private fun countdownTimeResend(seconds: String) {
        val time = seconds.formatToLong()
        if(time <= 0L) return
        binding.apply {
            tvResend.text = getString(R.string.login_seconds_resend, seconds)
            tvResend.isEnabled = false
            countDownTimer?.cancel()
            countDownTimer = object : CountDownTimer((time * 1000), 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    tvResend.text = getString(R.string.login_seconds_resend, (millisUntilFinished/1000).toString())
                }
                override fun onFinish() {
                    enableResendButton()
                }
            }.start()
        }
    }

    private fun handleManyRequestVerifyOTP(message: String) {
        setLayoutTextErrorLine(hasError = true, messageError = message.ifBlank { getString(R.string.new_otp_many_request_verify)})
    }

    private fun handleManyRequestSendOtp(seconds: String, message: String) {
        val time = seconds.formatToLong()
        if (time <= 0L) {
            enableResendButton()
        } else {
            startCountdown(seconds.formatToInt())
//            countdownTimeResend(seconds)
        }
        showSnackbar(message.ifBlank { getString(R.string.new_otp_rate_limit) })
    }

    private fun handleErrorRequestOtp(message: String) {
        showSnackbar(message)
        val enterOTPSubtitle =
            if (safeArgs.otpType == VerifySaleMode) {
                getString(R.string.delete_account_input_email_error__des, safeArgs.email)
            } else {
                getString(
                    R.string.delete_account_input_otp_error_des,
                    replacePhoneNumberParts(viewModel.userPhone())
                )
            }
        Utils.fromHtml(binding.tvInputOtpSubtitle, enterOTPSubtitle)

    }
    private fun showWarningDeleteAccountFail(title: String = "", message: String) {
        showWarningMessageDialog(title = getString(R.string.notification),
            textConfirm = getString(R.string.btn_accept),
            message = message,
            isCancelled = true,
            onConfirm = {
                navigateToAccountInfo()
            })
    }

    private fun navigateToAccountInfo() {
        activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
            NavAccountInfoDirections.actionGlobalToAccountInfo())
    }

    private fun navigateToMega(isRequiredLogin:Boolean =false ,message: String){
        if (isRequiredLogin){
            activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
                NavHomeMainDirections.actionGlobalToHome())
            setFragmentResult(Constants.REFRESH_DATA, bundleOf())
        }else{
            showWarningMessageDialog(title = getString(R.string.notification), textConfirm = getString(R.string.understood), message = message, isCancelled = true,onConfirm = {
                activity?.findNavHostFragment()?.findNavController()?.navigateSafe(
                    NavHomeMainDirections.actionGlobalToHome())
                setFragmentResult(Constants.REFRESH_DATA, bundleOf())
            })
        }
    }

    private fun logOut(isRequiredLogin: Boolean=false,msgSuccess: String= "") {
        if (!isRequiredLogin){
            // note : Delete account info successfully
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo,
                    logId = "198",
                    appId = TrackingUtil.currentAppId,
                    appName = TrackingUtil.currentAppName,
                    screen = "DeactivateAccount",
                    event = "DeactivateSuccess",
                    status = "Success"
                )
            )
        }
        AdsUtils.saveUserType(sharedPreferences, false)
        // send log Logout
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = "180",
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = "Logout",
                event = "Logout"
            )
        )
        Utils.clearUserData(sharedPreferences)
        trackingInfo.updateUserSession(0)
        trackingInfo.updateUserPhone("")
        trackingInfo.updateUserContract("")
        trackingInfo.updateUserId("")
        ZendeskUtils.updateZendeskIdentity(userLogin = false, userToken = "", userTokenType = "")
        navigateToMega(isRequiredLogin = isRequiredLogin ,message = msgSuccess?:"")

        // Picture in Picture
        PlayerPiPHelper.saveSupportPictureInPicture(sharedPreferences, isSupport = false)
    }
    @SuppressLint("SetTextI18n")
    private fun startCountdown(seconds: Int) {
        binding.tvResend.hide()
        binding.tvResendDes.text = getString(R.string.login_resend_otp_des, "(${seconds})")
        var timeCountdown = seconds
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(timeCountdown * 1000L, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeCountdown--
                if (_binding!=null){
                    binding.tvResendDes.text = getString(R.string.login_resend_otp_des,"(${timeCountdown}s)")
                }
            }
            override fun onFinish() {
                if (_binding!=null){
                    binding.tvResendDes.text = getString(R.string.login_not_have_otp)
                    binding.tvResend.show()
                }
            }
        }.start()
    }
    private fun replacePhoneNumberParts(originalText: String): String {
        val phoneNumberRegex = """(\d{3})[.\s]?(\d{3})[.\s]?(\d{4})""".toRegex()
        return originalText.replace(phoneNumberRegex) { matchResult ->
            val (areaCode, centralOfficeCode, lineNumber) = matchResult.destructured
            "${areaCode.take(3) + "*".repeat(centralOfficeCode.length)}${lineNumber}"
        }
    }
    private fun setLayoutTextErrorLine(
        hasError: Boolean=false,
        messageError:String?= getString(R.string.delete_account_text_error_otp)){
        binding.edtInputOtp.isActivated = hasError
        binding.underlineError.isVisible = hasError
        if(hasError) binding.underlineError.text = messageError

    }
    private fun getOTP(sms: String): String{
        return sms.substring(0, sms.length - 11).filter { it.isDigit() }
    }
    private fun showToast(msg: String) {
        context?.let {
            Toast.makeText(it, msg, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onOTPReceived(otp: String?) {
        otp?.apply {
            if(_binding != null) {
                binding.edtInputOtp.setText(getOTP(otp))
            }
            if (smsReceiver != null) {
                activity?.unregisterReceiver(smsReceiver)
                smsReceiver = null
            }
            verifyOtp()
        }
    }
    override fun onOTPTimeOut() {
        showToast("OTP Time out")
    }
    override fun onOTPReceivedError(error: String?) {
        error?.apply {
            showToast(this)
        }
    }
    private fun startSMSListener() {
        try {
            smsReceiver?.apply {
                activity?.unregisterReceiver(smsReceiver)
                smsReceiver = null
            }
            smsReceiver = SMSReceiver()
            smsReceiver?.setOTPListener(this)
            val intentFilter = IntentFilter()
            intentFilter.addAction(SmsRetriever.SMS_RETRIEVED_ACTION)
            context?.let {
                ContextCompat.registerReceiver(it, smsReceiver, intentFilter, ContextCompat.RECEIVER_EXPORTED)
            }
            val client = SmsRetriever.getClient(requireActivity())
            val task = client.startSmsRetriever()
            task.addOnSuccessListener {
                // API successfully started
                Logger.d("addOnSuccessListener")
            }
            task.addOnFailureListener {
                // Fail to start API
                Logger.d("addOnFailureListener")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}
