package com.fptplay.mobile.features.mqtt.model

import com.fptplay.mobile.features.mqtt.model.MqttAutomaticRetry.Companion.emptyMqttAutomaticRetry
import com.fptplay.mobile.features.mqtt.model.MqttConfig.Companion.emptyMqttConfig
import com.fptplay.mobile.features.mqtt.model.MqttNotificationConfig.Companion.emptyMqttNotificationConfig
import com.google.gson.annotations.SerializedName
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig

data class MqttNotification(
    @SerializedName("module")
    val module: String = "",

    @SerializedName("detail")
    var detail: MqttNotificationDetail? = null
)

data class MqttNotificationDetail(
    @SerializedName("notification_type")
    val notificationType: String = "",

    @SerializedName("mqtt")
    val mqtt: MqttNotificationConfig? = null
){
    companion object{
        @JvmStatic
        val emptyMqttNotificationDetail = MqttNotificationDetail(
            notificationType = "",
            mqtt = emptyMqttNotificationConfig
        )
    }
}
data class MqttConfig(
    @SerializedName("expires")
    var expires: String = "",

    @SerializedName("jwtFrom")
    var jwtFrom: String = "",

    @SerializedName("rooms")
    var option: MQTTConfig.Option?,

    @SerializedName("token")
    var token: String = "",

    @SerializedName("url")
    var url: MQTTConfig.Url?,

){
    companion object{
        @JvmStatic
        val emptyMqttConfig = MqttConfig(
            expires = "",
            jwtFrom = "",
            option = MQTTConfig.Option(),
            token = "",
            url = MQTTConfig.Url()
        )
    }
}

data class MqttNotificationConfig(
    @SerializedName("enable")
    var enable: Boolean = false,
    @SerializedName("emmenency_rooms")
    var rooms: List<MqttRoom> ,

    @SerializedName("automatic_retry")
    var automaticRetry: MqttAutomaticRetry?,

    @SerializedName("options")
    var options: List<MqttModeOption>,

    @SerializedName("config")
    var config: MqttConfig?
){
    companion object{
        @JvmStatic
        val emptyMqttNotificationConfig = MqttNotificationConfig(
            enable = false,
            rooms = listOf(),
            automaticRetry = emptyMqttAutomaticRetry,
            options = emptyList(),
            config = emptyMqttConfig
        )
        val emptyMqttRoomList = listOf(
            MqttRoom(
                roomId = "android",
                roomType = "mqtt"
            ),
            MqttRoom(
                roomId = "android-beta",
                roomType = "mqtt"
            )
        )

    }
}

data class MqttAutomaticRetry(
    @SerializedName("enable")
    var enable: Boolean = false,

    @SerializedName("max_retry_interval")
    var maxRetryInterval: Int = -1,

    @SerializedName("min_retry_interval")
    var minRetryInterval: Int = -1,

    @SerializedName("random")
    var random: Int = -1
){
    companion object{
        @JvmStatic
        val emptyMqttAutomaticRetry = MqttAutomaticRetry(
            enable = false,
            maxRetryInterval = -1,
            minRetryInterval = -1,
            random = -1
        )

    }
}

data class MqttModeOption(
    @SerializedName("mqtt_mode")
    var mqttMode: Int = 1,

    @SerializedName("waiting_approval")
    var waitingApproval: Int? = null,

    @SerializedName("enable_backup_api")
    var enableBackupApi: Int? = null,

    @SerializedName("max_retry_backup_api")
    var maxRetryBackupApi: Int? = null,

    @SerializedName("preview_waiting_approval")
    var previewWaitingApproval: Int? = null
){
    companion object{
        @JvmStatic
        val emptyMqttModeOption = MqttModeOption(
            mqttMode = 1,
            waitingApproval = null,
            enableBackupApi = null,
            maxRetryBackupApi = null,
            previewWaitingApproval = null
        )
    }
}

data class MqttRoom(
    @SerializedName("room_id")
    var roomId: String = "",
    @SerializedName("room_type")
    var roomType: String = "",
){
    companion object{
        @JvmStatic
        val emptyMqttRoom = MqttRoom(
            roomId = "",
            roomType = ""
        )
    }
}

