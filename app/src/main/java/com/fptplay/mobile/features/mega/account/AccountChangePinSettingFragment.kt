package com.fptplay.mobile.features.mega.account

import android.os.Bundle
import android.text.InputType
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.Toast
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.hideKeyboard
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialog
import com.fptplay.mobile.common.ui.popup.alert_dialog.AlertDialogListener
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.databinding.AccountChangePinSettingFragmentBinding
import com.fptplay.mobile.features.login.gmsotp.SMSReceiver
import com.fptplay.mobile.features.mega.MegaViewModel
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.entity.fplay.otp.UserOtpType
import com.xhbadxx.projects.module.util.common.Util.hide
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class AccountChangePinSettingFragment : BaseFragment<MegaViewModel.MegaState, MegaViewModel.MegaIntent>(){

    override val handleBackPressed = true
    override val viewModel by activityViewModels<MegaViewModel>()
    private var _binding: AccountChangePinSettingFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: AccountChangePinSettingFragmentArgs by navArgs()
    private val requiredPasswordLength by lazy { 6 }

    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    //endregion

    //region Overrides
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountChangePinSettingFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
    override fun bindComponent() {
        binding.apply {

            pvNewPinSetting.setHint(getString(R.string.account_create_change_pin_setting_hint))
            pvNewPinSetting.setMaxLength(requiredPasswordLength)
            pvNewPinSetting.setTypeInputEditText(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD)
//            pvNewPinSetting.setError(null)

            pvNewPinSettingConfirm.setHint(getString(R.string.account_create_change_pin_setting_confirm_hint))
            pvNewPinSettingConfirm.setMaxLength(requiredPasswordLength)
            pvNewPinSettingConfirm.setTypeInputEditText(InputType.TYPE_CLASS_NUMBER or InputType.TYPE_NUMBER_VARIATION_PASSWORD)
//            pvNewPinSettingConfirm.setError(null)
            root.onClickDelay {
                hideKeyboard()
            }
        }
        bindScreen()
    }
    override fun bindEvent() {
        binding.apply {
            toolbar.setNavigationOnClickListener {
//                findNavController().navigate(AccountChangePasswordFragmentDirections.accountChangePasswordFragmentToAccountInfoFragment())
                backHandler()
                parentFragment?.parentFragment?.setFragmentResult(Constants.ACCOUNT_TOOLBAR_HANDLE_BACK_COMPLETELY_EVENT, bundleOf())
            }

            pvNewPinSetting.passwordEdittext.doAfterTextChanged {
                updateConfirmButtonBackground()
            }
            pvNewPinSettingConfirm.passwordEdittext.doAfterTextChanged {
                updateConfirmButtonBackground()
            }

            pvNewPinSettingConfirm.passwordEdittext.setOnEditorActionListener { _, actionId, _ ->
                if (actionId == EditorInfo.IME_ACTION_DONE) binding.btnConfirm.performClick()
                false
            }
            btnConfirm.setOnClickListener {
                activity?.currentFocus?.hideKeyboard()
                verifyAndChangePassword()

            }
        }
        setFragmentResultListener(Constants.LOGIN_SUCCESS_FOR_DIALOG) { _, bundle ->
            val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
            when {
                isSuccess -> {}
                else -> backHandler()
            }
        }
    }

    override fun backHandler() {
//        findNavController().navigateUp()
        Timber.tag("tam-account").d("backHandler ${safeArgs.popupToId } - ${safeArgs.popupToInclusive }")
        if(safeArgs.popupToId != -1) {
            findNavController().popBackStack(safeArgs.popupToId, safeArgs.popupToInclusive)
        } else {
            findNavController().navigateUp()

        }
    }

    override fun observeState() {
        super.observeState()
        Timber.tag("tam-account").d("loginViewModel observeState")

    }

    override fun MegaViewModel.MegaState.toUI() {
        Timber.tag("tam-account").d("${<EMAIL>} MegaState: $this")
        when (this) {
            is MegaViewModel.MegaState.Loading -> showLoading(true)
            is MegaViewModel.MegaState.ResultChangePinSettingOtpV1 -> {
                trackingProxy.sendEvent(
                    InforMobile(
                        infor = trackingInfo,
                        logId = "198",
                        appId = TrackingUtil.currentAppId,
                        appName = TrackingUtil.currentAppName,
                        screen = "ModifiedInformation",
                        event = "ChangePassword",
                        status = if (data.isSuccess()) "Success" else "Failed"
                    ))

                if (data.isSuccess()) {
                    resetUi()
                    val message = getString(R.string.set_pin_setting_success)

                    backHandler()
                    setFragmentResult(AccountInfoFragment.REQUEST_UPDATE_PASSWORD, bundleOf())
                    showToast(message)

                } else {
                    if (data.messageCode == "12") {
                        if(data.message.isNotBlank()) {
                            binding.pvNewPinSettingConfirm.setErrorNoHighlight(data.message)
                        } else {
                            showToast(getString(
                                R.string.new_pin_setting_error_default_message
                            ))
                        }
                    } else{
                        if (data.message.isNotBlank()) {
                            showSnackbar(data.message)
//                            showWarningDialog(data.message)
                        } else {
                            showToast(getString(
                                R.string.new_pin_setting_error_default_message
                            ))
                        }
                    }
                }
            }
            is MegaViewModel.MegaState.ResultCreatePinSetting -> {
                if (data.status == "1") {
                    resetUi()
                    sharedPreferences.setShouldShowPopUpPin(sharedPreferences.userId(), false)
                    val message = getString(R.string.set_pin_setting_success)

                    setFragmentResult(AccountInfoFragment.REQUEST_UPDATE_PASSWORD, bundleOf())
                    showToast(message)
                    backHandler()

                } else {
                    when(data.errorCode) {
                        "6" -> {
                            // đã có mã quản lý
                            showWarningDialog(
                                message = data.message,
                                onClose = {
                                    backHandler()
                                }
                            )
                        }
                        "4" -> {
                            // CASE MQL đơn giản || trùng mật khẩu cũ
                            if(data.message.isNotBlank()) {
                                binding.pvNewPinSettingConfirm.setErrorNoHighlight(data.message)
                            } else {
                                showToast(getString(
                                    R.string.new_pin_setting_error_default_message
                                ))
                            }
                        }
                        else -> {
                            if (data.message.isNotBlank()) {
                                showSnackbar(data.message)
//                            showWarningDialog(data.message)
                            } else {
                                showToast(getString(
                                    R.string.new_pin_setting_error_default_message
                                ))
                            }
                        }
                    }
                }

            }
            is MegaViewModel.MegaState.ResultCreateUserPinSettingNewFlowOtp -> {
                if (data.status == "1") {
                    resetUi()
                    sharedPreferences.setShouldShowPopUpPin(sharedPreferences.userId(), false)
                    val message = data.msg.ifBlank { getString(R.string.set_pin_setting_success) }

                    setFragmentResult(AccountInfoFragment.REQUEST_UPDATE_PASSWORD, bundleOf())
                    showToast(message)
                    backHandler()

                } else {
                    when(data.errorCode) {
                        "9" ->{
                            // CASE Phiên làm việc hết hạn
                            showWarningDialog(
                                textTitle = errorMessageExpireToken(message = data.title, messageDefault = getString(R.string.login_title_token_expire)),
                                message = errorMessageExpireToken(message = data.msg, messageDefault = getString(R.string.login_des_token_expire)),
                                isOnlyConfirmButton = true,
                                textConfirm = getString(R.string.close),
                                onConfirm = {
                                    backHandler()
                                }
                            )
                        }
                        "6" -> {
                            // CASE đã có mã quản lý || MQL trùng với MQL cũ
                            if(data.msg.isNotBlank()) {
                                binding.pvNewPinSetting.setError(data.msg)
                            } else {
                                showToast(getString(
                                    R.string.new_pin_setting_error_default_message
                                ))
                            }
                        }
                        "4" -> {
                            // CASE MQL đơn giản || trùng mật khẩu cũ || không hợp lệ
                            if(data.msg.isNotBlank()) {
                                binding.pvNewPinSetting.setError(data.msg)
                            } else {
                                showToast(getString(
                                    R.string.new_pin_setting_error_default_message
                                ))
                            }
                        }
                        "5" -> {
                            // CASE MQL không trùng khớp | password != passwordConfirm
                            if(data.msg.isNotBlank()) {
                                binding.pvNewPinSettingConfirm.setError(data.msg)
                            } else {
                                showToast(getString(
                                    R.string.new_pin_setting_error_default_message
                                ))
                            }
                        }
                        else -> {
                            if (data.msg.isNotBlank()) {
                                showSnackbar(data.msg)
//                            showWarningDialog(data.message)
                            } else {
                                showToast(getString(
                                    R.string.new_pin_setting_error_default_message
                                ))
                            }
                        }
                    }
                }

            }
            is MegaViewModel.MegaState.Error -> {
                showWarningDialog(message = message)
            }
            is MegaViewModel.MegaState.ErrorRequiredLogin -> {
                Timber.tag("tam-account").d("Change password ErrorRequiredLogin: $message")
//                navigateToLogin()
                navigateToLoginWithParams(isDirect = true)

            }
            is MegaViewModel.MegaState.Done -> showLoading(false)

            is MegaViewModel.MegaState.ErrorNoInternet->{
                showWarningDialog(message = message)
            }
            else -> {}
        }
    }
    //endregion

    //region Commons

    private fun showWarningMessageDialogHandleBack(
        title: String? = null,
        message: String,
        textConfirm: String? = null,
        isCancelled: Boolean = false,
        onConfirm: (() -> Unit)? = null,
        onBackPressed: (() -> Unit)? = null) {
        Timber.tag("tam-account").d("${<EMAIL>} showWarningMessageDialogHandleBack: $this")
        AlertDialog().apply {
            setShowTitle(true)
            setTextTitle(title ?: <EMAIL>(R.string.notification))
            setOnlyConfirmButton(true)
            setHandleBackPress(isCancelled)
            setMessage(message)
            setTextConfirm(textConfirm ?: <EMAIL>(R.string.confirm))
            setListener(object : AlertDialogListener {
                override fun onConfirm() {
                    onConfirm?.invoke()
                }

                override fun onBackPress() {
                    onBackPressed?.invoke()
                }
            })
            isCancelable = false
        }.show( <EMAIL>, "AlertDialog")
    }
    private fun bindScreen() {
        Timber.tag("tam-account").d("${<EMAIL>} bindScreen")
        binding.layoutChangePassword.show()



    }
    private var smsReceiver: SMSReceiver? = null

    private fun showToast(msg: String) {
        Toast.makeText(requireContext(), msg, Toast.LENGTH_LONG).show()
    }

    private fun updateConfirmButtonBackground() {
//        val otp = binding.edtOtp.text
        val newPassword = binding.pvNewPinSetting.password
        val newPasswordAgain = binding.pvNewPinSettingConfirm.password

        val isEnable = newPassword?.length == requiredPasswordLength && newPasswordAgain?.length == requiredPasswordLength

        if (isEnable) {
            binding.btnConfirm.background = AppCompatResources.getDrawable(requireContext(), R.drawable.account_rounded_btn_background_enable)
            binding.btnConfirm.setTextColor(ContextCompat.getColor(requireContext(), R.color.app_content_text_color))
        } else {
            binding.btnConfirm.background = AppCompatResources.getDrawable(requireContext(), R.drawable.account_rounded_btn_background_disable)
            binding.btnConfirm.setTextColor(ContextCompat.getColor(requireContext(), R.color.app_content_text_disable_color))
        }
    }

    private fun verifyAndChangePassword() {
        val newPassword = binding.pvNewPinSetting.password
        val newPasswordAgain = binding.pvNewPinSettingConfirm.password
        binding.pvNewPinSetting.setError("")
        binding.pvNewPinSettingConfirm.setError("")
        when {
            newPassword.isNullOrBlank() -> {
                return
            }
            newPasswordAgain.isNullOrBlank() -> {
                return
            }
        }
        if(safeArgs.createPinSetting) {
//            viewModel.dispatchIntent(
//                MegaViewModel.MegaIntent.CreatePinSetting(
//                    newPassword.toString(),
//                    newPasswordAgain.toString()
//                )
//            )
            viewModel.dispatchIntent(
                MegaViewModel.MegaIntent.UpdateUserPinSettingNewFlowOtp(
                    pass = newPassword.toString(),
                    passConfirm = newPasswordAgain.toString(),
                    otpType = UserOtpType.LoginSetPass1st,
                    verifyToken = ""    // hard code verifyToken verifyToken = "" for type LoginSetPass1st
                )
            )

        } else {
            viewModel.dispatchIntent(
                MegaViewModel.MegaIntent.UpdateUserPinSettingNewFlowOtp(
                    pass = newPassword.toString(),
                    passConfirm = newPasswordAgain.toString(),
                    otpType = UserOtpType.LoginChangePass,
                    verifyToken = safeArgs.verifyToken,
                )
            )
//            viewModel.dispatchIntent(
//                MegaViewModel.MegaIntent.ChangePinSettingOtpV1(
//                    safeArgs.verifyToken,
//                    RequestOtp.CHANGE_PASS,
//                    newPassword.toString(),
//                    newPasswordAgain.toString()
//                )
//            )
        }
    }


    private fun showLoading(isShowed: Boolean = false) {
        binding.apply {
            if (isShowed) {
                pbLoading.root.show()
            } else {
                pbLoading.root.hide()
            }
        }
    }

    private fun clearError() {
//        binding.pvOldPassword.setError(null)
        binding.pvNewPinSetting.setError(null)
        binding.pvNewPinSettingConfirm.setError(null)
    }

    private fun resetUi() {
        binding.apply {
            showLoading(false)
            binding.pvNewPinSetting.setPasswordText("")
            binding.pvNewPinSettingConfirm.setPasswordText("")
        }
    }

    private fun errorMessageExpireToken(message: String?, messageDefault: String) :String{
        return if (message.isNullOrBlank()) {
            messageDefault
        } else {
            message
        }
    }
    //endregion
}