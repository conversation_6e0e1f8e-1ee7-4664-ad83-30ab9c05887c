package com.fptplay.mobile.features.mega.account

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton.OnCheckedChangeListener
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.onClickDelay
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.AccountInfoUpdateEmailFragmentBinding
import com.fptplay.mobile.features.mega.account.model.AccountOtpTargetScreen
import com.fptplay.mobile.features.pladio.util.context
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject


@AndroidEntryPoint
class AccountInfoUpdateEmailFragment :
    BaseFragment<AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState, AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent>() {
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override val viewModel: AccountInfoUpdateEmailViewModel by viewModels()

    private var _binding: AccountInfoUpdateEmailFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: AccountInfoUpdateEmailFragmentArgs by navArgs()
    override val hasEdgeToEdge = true

    private val onCheckChangeListener: OnCheckedChangeListener =
        OnCheckedChangeListener { buttonView, isChecked ->
            binding.btnContinue.isEnabled = sharedPreferences.isEnableSalesMode() != isChecked
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = AccountInfoUpdateEmailFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            findNavController().navigateUp()
        }

        binding.btnContinue.onClickDelay {
            if (binding.cbFpter.isChecked) {
                viewModel.dispatchIntent(AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent.RequestValidateEmailUser)
            } else {
                showConfirmLogoutSaleMode()
            }
        }

        binding.cbFpter.setOnCheckedChangeListener(onCheckChangeListener)
    }

    override fun bindComponent() {
        binding.tvEmail.text = safeArgs.userEmail
        binding.cbFpter.isChecked = sharedPreferences.isEnableSalesMode()
    }

    private fun navigateToOTPFragment(verifyToken: String, userEmail: String) {
        findNavController().navigate(
            AccountInfoUpdateEmailFragmentDirections.actionAccountInfoUpdateEmailFragmentToAccountOtpV2Fragment(
                isPackage = false,
                verifyToken = verifyToken,
                email = userEmail,
                targetScreen = AccountOtpTargetScreen.VerifySaleMode
            )
        )
    }

    private fun showConfirmLogoutSaleMode() {
        showAlertDialog(
            showTitle = true,
            title = binding.context.getString(R.string.disable_sale_mode_title),
            message = binding.context.getString(R.string.disable_sale_mode_detail),
            textConfirm = context?.getString(R.string.alert_confirm),
            textClose = context?.getString(R.string.later),
            onConfirm = {
                viewModel.dispatchIntent(AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent.LogoutSaleMode)
            }
        )
    }

    override fun AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.toUI() {
        when (this) {
            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.Loading -> {
                when (intent) {
                    is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent.RequestValidateEmailUser -> {
                        showLoading()
                    }

                    is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent.LogoutSaleMode -> {
                        showLoading()
                    }

                    else -> {}
                }
            }

            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.ResultValidateEmailUser -> {
                navigateToOTPFragment(
                    this.data.validateData.verifyToken,
                    safeArgs.userEmail
                )
            }

            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.ResultLogoutSaleMode -> {
                Utils.restartApp(requireContext())
            }

            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.ErrorByInternet -> {
//                showNoInternetView()
                context?.let {
                    showWarningDialog(
                        message = message,
                        isShowTitle = true,
                        isOnlyConfirmButton = true,
                        textConfirm = it.getString(R.string.close),
                    )
                }
            }

            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.ErrorRequiredLogin -> {
                navigateToLoginWithParams(
                    isDirect = false,
                    requestRestartApp = true
                )
            }

            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.Error -> {
                context?.let {
                    showWarningDialog(
                        textTitle = title,
                        message = message,
                        isShowTitle = title.isNotBlank(),
                        isOnlyConfirmButton = true,
                        textConfirm = it.getString(R.string.close),
                    )
                }
            }

            is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState.Done -> {
                when (intent) {
                    is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent.RequestValidateEmailUser -> {
                        hideLoading()
                    }

                    is AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent.LogoutSaleMode -> {
                        hideLoading()
                    }

                    else -> {}
                }
            }

            else -> {}
        }
    }
}