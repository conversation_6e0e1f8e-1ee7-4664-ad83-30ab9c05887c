package com.fptplay.mobile.features.mega.account

import androidx.lifecycle.viewModelScope
import com.fptplay.mobile.common.interfaces.ViewIntent
import com.fptplay.mobile.common.interfaces.ViewState
import com.fptplay.mobile.common.ui.bases.BaseViewModel
import com.fptplay.mobile.common.utils.Utils
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.Status
import com.xhbadxx.projects.module.domain.entity.fplay.loginv2.ValidateEmailUser
import com.xhbadxx.projects.module.domain.repository.fplay.LoginRepositoryV2
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AccountInfoUpdateEmailViewModel @Inject constructor(
    private val loginRepositoryV2: LoginRepositoryV2,
    private val sharedPreferences: SharedPreferences,
) : BaseViewModel<AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailIntent, AccountInfoUpdateEmailViewModel.AccountInfoUpdateEmailState>() {
    override fun dispatchIntent(intent: AccountInfoUpdateEmailIntent) {
        viewModelScope.launch {
            when (intent) {
                is AccountInfoUpdateEmailIntent.RequestValidateEmailUser -> {
                    requestValidateEmailUser(
                        intent = intent
                    )
                }

                is AccountInfoUpdateEmailIntent.LogoutSaleMode -> {
                    requestLogoutSaleMode(
                        intent = intent
                    )
                }
            }
        }
    }

    private suspend fun requestValidateEmailUser(intent: AccountInfoUpdateEmailIntent.RequestValidateEmailUser) {
        loginRepositoryV2.validateEmailUser(
            phone = sharedPreferences.userPhone(),
            type = "otp_email"
        ).collect {
            _state.value = it.reduce(intent = intent) { isCached, validateEmailUser ->
                if(validateEmailUser.status == "1") {
                    AccountInfoUpdateEmailState.ResultValidateEmailUser(
                        data = validateEmailUser,
                        intent = intent
                    )
                } else {
                    AccountInfoUpdateEmailState.Error(
                        message = validateEmailUser.message,
                        title = validateEmailUser.validateData.title,
                        intent = intent
                    )
                }
            }
        }
    }

    private suspend fun requestLogoutSaleMode(intent: AccountInfoUpdateEmailIntent.LogoutSaleMode?) {
        loginRepositoryV2.logoutSaleMode(
            phone = sharedPreferences.userPhone()
        ).collect {
            _state.value = it.reduce(intent = intent) { isCached, status ->
                Utils.clearUserData(sharedPreferences = sharedPreferences, clearSaleMode = true)
                AccountInfoUpdateEmailState.ResultLogoutSaleMode(data = status, intent = intent)
            }
        }
    }

    override fun <T> Result<T>.reduce(
        intent: AccountInfoUpdateEmailIntent?,
        successFun: (Boolean, T) -> AccountInfoUpdateEmailState
    ): AccountInfoUpdateEmailState {
        return when (this) {
            is Result.Init -> AccountInfoUpdateEmailState.Loading(intent = intent)
            is Result.Success -> {
                successFun(this.isCached, this.successData)
            }
            is Result.UserError.RequiredLogin -> AccountInfoUpdateEmailState.ErrorRequiredLogin(
                intent = intent,
                message = message,
                requiredLogin = true
            )
            is Result.Error.Intenet -> AccountInfoUpdateEmailState.ErrorByInternet(
                message = this.message,
                intent = intent
            )

            is Result.Error -> AccountInfoUpdateEmailState.Error(
                message = this.message,
                title = "",
                intent = intent
            )

            Result.Done -> AccountInfoUpdateEmailState.Done(intent = intent)
        }
    }


    sealed class AccountInfoUpdateEmailState : ViewState {
        data class Loading(val intent: AccountInfoUpdateEmailIntent?) :
            AccountInfoUpdateEmailState()

        data class Error(val message: String, val title: String, val intent: AccountInfoUpdateEmailIntent?) :
            AccountInfoUpdateEmailState()

        data class ErrorByInternet(val message: String, val intent: AccountInfoUpdateEmailIntent?) :
            AccountInfoUpdateEmailState()

        data class ErrorRequiredLogin(
            val message: String,
            val intent: AccountInfoUpdateEmailIntent?,
            val requiredLogin: Boolean
        ) : AccountInfoUpdateEmailState()

        data class ResultValidateEmailUser(
            val data: ValidateEmailUser,
            val intent: AccountInfoUpdateEmailIntent?
        ) : AccountInfoUpdateEmailState()

        data class ResultLogoutSaleMode(
            val data: Status,
            val intent: AccountInfoUpdateEmailIntent?
        ) : AccountInfoUpdateEmailState()

        data class Done(val intent: AccountInfoUpdateEmailIntent?) : AccountInfoUpdateEmailState()
    }

    sealed class AccountInfoUpdateEmailIntent : ViewIntent {
        object RequestValidateEmailUser : AccountInfoUpdateEmailIntent()
        object LogoutSaleMode : AccountInfoUpdateEmailIntent()
    }
}