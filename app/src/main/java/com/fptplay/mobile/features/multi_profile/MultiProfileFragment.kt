package com.fptplay.mobile.features.multi_profile

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.setFragmentResultListener
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import androidx.recyclerview.widget.RecyclerView
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.NavHomeMainDirections
import com.fptplay.mobile.R
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.extensions.showSnackBar
import com.fptplay.mobile.common.ui.bases.BaseFragment
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.Navigation.navigateToLoginWithParams
import com.fptplay.mobile.common.utils.Utils
import com.fptplay.mobile.databinding.MultiProfileFragmentBinding
import com.fptplay.mobile.features.multi_profile.adapter.MultiProfileAdapter
import com.fptplay.mobile.features.multi_profile.utils.EditProfileScreenType
import com.fptplay.mobile.features.multi_profile.utils.FlexBoxLayoutManagerWithLimit
import com.fptplay.mobile.features.multi_profile.utils.MultiProfileUtils
import com.fptplay.mobile.features.multi_profile.utils.PinProfileScreenType
import com.fptplay.mobile.features.multi_profile.utils.ResetPasswordScreenTargetType
import com.fptplay.mobile.features.multi_profile.utils.StartingTargetScreenType
import com.fptplay.mobile.features.multi_profile.utils.TrackingLogProfile
import com.fptplay.mobile.player.utils.gone
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.JustifyContent
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.xhbadxx.projects.module.domain.entity.fplay.user.LoginProfile
import com.xhbadxx.projects.module.domain.entity.fplay.user.Profile
import com.xhbadxx.projects.module.domain.entity.fplay.user.ProfileDetail
import com.xhbadxx.projects.module.domain.entity.fplay.user.ProfileItem
import com.xhbadxx.projects.module.util.common.IEventListener
import com.xhbadxx.projects.module.util.common.Util.show
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class MultiProfileFragment :
    BaseFragment<MultiProfileViewModel.MultiProfileState, MultiProfileViewModel.MultiProfileIntent>() {

    @Inject
    lateinit var sharedPreferences: SharedPreferences

    @Inject
    lateinit var trackingProxy: TrackingProxy

    @Inject
    lateinit var trackingInfo: Infor

    override val viewModel: MultiProfileViewModel by activityViewModels()
    private var _binding: MultiProfileFragmentBinding? = null
    private val binding get() = _binding!!
    private val safeArgs: MultiProfileFragmentArgs by navArgs()

    private val multiProfileAdapter by lazy {
        MultiProfileAdapter(
            currentProfileId = sharedPreferences.profileId(),
            currentProfileType = sharedPreferences.profileType()
        )
    }


    override val handleBackPressed = true
    private var oldType = ""
    private var oldId = ""

    private var title: String = ""
    private val maxProfile by lazy { MainApplication.INSTANCE.appConfig.profileMaxItem }
    private val itemInRow: Int by lazy { 2 }
    private val itemInRowLandscapeTablet: Int by lazy { 5 }
    private var itemDecoration: RecyclerView.ItemDecoration? = null
    private var lastOrientation = Configuration.ORIENTATION_UNDEFINED
    private var inEditMode = false
        set(value) {
            multiProfileAdapter.inEditMode = value
            field = value
            setToolbarEdit()
        }
    private var navigationId: Int = R.id.nav_multi_profile
    private var previousBackStackEntryId: Int = -1
    private var isSelectionOnBoarding: Boolean = false
    private var isFromLoginScreen: Boolean = false
    private var requestRestartApp: Boolean = false


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = MultiProfileFragmentBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun showPageError(
        parentView: View?,
        title: String?,
        errorMessage: String,
        navigationIcon: Int?
    ) {
        super.showPageError(
            parentView,
            title,
            errorMessage,
            navigationIcon ?: R.drawable.ic_arrow_left
        )
    }

    override fun bindComponent() {

        updateRecyclerViewContent(
            orientation = MainApplication.INSTANCE.resources.configuration.orientation, init = true
        )
        binding.rvProfile.apply {
            adapter = multiProfileAdapter
        }
    }

    override fun bindData() {
        hidePageError()
        if (multiProfileAdapter.size() == 0) {
            title = getString(R.string.multi_profile_choose_profile_title)
            // get list profile
            viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.GetListProfile())
            //send tracking log when access list Profile
            TrackingLogProfile.sendLogAccessProfile()
        }

        oldType = sharedPreferences.profileType()
        oldId = sharedPreferences.profileId()
        navigationId = arguments?.getInt(Constants.NAVIGATION_ID, R.id.nav_multi_profile) ?: R.id.nav_multi_profile // set value default
        previousBackStackEntryId = arguments?.getInt(Constants.PREVIOUS_BACK_STACK_ENTRY_ID, -1) ?: -1 // set value default
        isSelectionOnBoarding = arguments?.getBoolean(Constants.IS_SELECTION_ON_BOARDING, false) ?: false
        isFromLoginScreen = arguments?.getBoolean(Constants.IS_FROM_LOGIN_SCREEN, false) ?: false
        requestRestartApp = arguments?.getBoolean(Constants.REQUEST_RESTART_APP, false) ?: false
        setToolbarEdit()

    }

    override fun bindEvent() {
        binding.toolbar.setNavigationOnClickListener {
            backHandler()
        }
        binding.toolbar.setOnMenuItemClickListener {
            switchEditMode()
            false
        }
        binding.btnManageProfile.setOnClickListener {
            findNavController().navigate(
                NavHomeMainDirections.actionGlobalToMultiProfile(
                    targetScreen = StartingTargetScreenType.ManageProfile
                )
            )
        }
        multiProfileAdapter.eventListener = object : IEventListener<ProfileItem> {
            override fun onClickedItem(position: Int, data: ProfileItem) {
                Timber.tag("tam-multiProfile").d("${this.javaClass.simpleName} onClickedItem $data")
                when (data) {
                    ProfileItem.AddProfile -> {
                        addProfile()
                    }

                    is Profile -> {
                        /**
                         * Note : if navigationId  from  feature login not support for kid profile then show warning message and not focus switch profile
                         * Bug jira : ANRMOBI-2236
                         * **/
                        if (isFromLoginScreen && !checkAppModuleSupportKids.isCheckModuleCurrentSupportKids(
                                isFromLoginScreen = isFromLoginScreen,
                                navigationId =  navigationId,
                                profile = data,
                                previousBackStackEntryId = previousBackStackEntryId
                            )
                        ) {
                            showWarningDialog(
                                textTitle = getString(R.string.multi_profile_kid_not_supported_title),
                                message = getString(R.string.multi_profile_kid_not_supported_feature),
                                textConfirm = getString(R.string.close),
                                isConfirmEnable = true,
                                isExitEnable = true,
                                isCancelOutside = true
                            )
                        } else {
                            val targetScreen =
                                if (inEditMode) EDIT_PROFILE_TARGET_SCREEN else LOGIN_PROFILE_TARGET_SCREEN
                            selectProfile(position, data, targetScreen)
                        }
                    }
                }
            }
        }
        if (context.isTablet()) {

            setFragmentResultListener(Constants.LOGIN_SUCCESS_FOR_DIALOG) { _, bundle ->
                Timber.e(" LOGIN_SUCCESS_FOR_DIALOG")
                val isSuccess = bundle.getBoolean(Constants.LOGIN_SUCCESS_KEY, false)
                when {
                    isSuccess -> bindData()
                    else -> {}
                }
                setFragmentResult(Constants.REFRESH_DATA, bundle)
            }

        }





        setFragmentResultListener(Utils.PROFILE_REFRESH_LIST_PROFILE_EVENT) { _, bundle ->
            val refresh = bundle.getBoolean(Utils.PROFILE_HAVE_CHANGE_DATA, false)
            if (refresh) {
                viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.GetListProfile())
                inEditMode = false
            }
        }
        setFragmentResultListener(Utils.PROFILE_ONBOARD_DISMISS_CHANGED_EVENT) { _, bundle ->
            navigateToEventAfterSwitchProfile(
                isFromLoginScreen = isFromLoginScreen,
                isSelectionOnBoarding = isSelectionOnBoarding,
                profile = viewModel.selectedProfile,
            )
        }
        setFragmentResultListener(Utils.PROFILE_ONBOARD_SWITCH_PROFILE_PIN_CHANGED_EVENT) { _, bundle ->
            Timber.e(" PROFILE_ONBOARD_SWITCH_PROFILE_PIN_CHANGED_EVENT")
            navigateToEventAfterSwitchProfile(
                isFromLoginScreen = isFromLoginScreen,
                isSelectionOnBoarding = isSelectionOnBoarding,
                profile = viewModel.selectedProfile,
                isBlockPin = true
            )
        }
    }

    private fun navigateToEventAfterSwitchProfile(
        isFromLoginScreen: Boolean,
        isSelectionOnBoarding: Boolean,
        profile: Profile?,
        isBlockPin: Boolean = false
    ) {
        profile?.let {
            if (isFromLoginScreen) {
                if (isBlockPin) {
                    // logic onboarding login profile
                    if (findNavController().currentDestination?.id == R.id.multiProfilePinProfileFragment) {
                        findNavController().navigateUp()
                    }
                    findNavController().previousBackStackEntry?.arguments?.putBoolean(
                        "isLoginProfileChanged",
                        true
                    )
                    setFragmentResult(
                        Utils.PROFILE_ONBOARD_SWITCH_PROFILE_CHANGED_EVENT,
                        bundleOf()
                    )
                } else {
                    findNavController().previousBackStackEntry?.arguments?.putBoolean(
                        "isLoginProfileChanged",
                        true
                    )
                    MultiProfileUtils.sendEventProfileOnboardingIfSwitchProfile(
                        activity = activity,
                        profile = it,
                        fragment = this,
                        sharedPreferences = sharedPreferences,
                        sourceChange = "${this.javaClass.simpleName} - backhandler",
                        oldProfileId = oldId,
                        oldProfileType = oldType
                    )
                    findNavController().navigateUp()
                }
            } else {
                MultiProfileUtils.switchProfile(
                    activity = activity,
                    sharedPreferences = sharedPreferences,
                    profile = it,
                    restartAppAfterSwitch = true,
                    restartKeepIntent = isSelectionOnBoarding
                )
            }
        }
    }

    override fun retryLoadPage() {
        // get list profile
        viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.GetListProfile())
    }

    override fun MultiProfileViewModel.MultiProfileState.toUI() {
        when (this) {
            is MultiProfileViewModel.MultiProfileState.Loading -> {
                if (intent is MultiProfileViewModel.MultiProfileIntent.GetListProfile ||
                    intent is MultiProfileViewModel.MultiProfileIntent.LoginProfile ||
                    intent is MultiProfileViewModel.MultiProfileIntent.GetProfileDetail ||
                    intent is MultiProfileViewModel.MultiProfileIntent.GetProfileDetailAndUpdateUserInfo
                ) {
                    showLoading()
                }
            }

            is MultiProfileViewModel.MultiProfileState.ErrorNoInternet -> {
                when (intent) {
                    is MultiProfileViewModel.MultiProfileIntent.GetListProfile -> {
                        showPageError(
                            title = title, errorMessage = MultiProfileUtils.errorMessage(
                                context = requireContext(),
                                message = message
                            )
                        )

                    }

                    is MultiProfileViewModel.MultiProfileIntent.GetProfileDetail,
                    is MultiProfileViewModel.MultiProfileIntent.GetProfileDetailAndUpdateUserInfo,
                    is MultiProfileViewModel.MultiProfileIntent.LoginProfile -> {
                        binding.root.showSnackBar(
                            title = MultiProfileUtils.errorMessage(
                                context = requireContext(),
                                message = message
                            )
                        )
                    }

                    else -> {}
                }
            }

            is MultiProfileViewModel.MultiProfileState.ErrorRequiredLogin -> {
                when (intent) {
                    is MultiProfileViewModel.MultiProfileIntent.GetListProfile -> {
                        // show page error beneath popup login
                        Timber.tag("tam-multiProfile")
                            .e("${this.javaClass.simpleName} ErrorRequiredLogin GetListProfile")
                        showPageError(
                            title = title, errorMessage = MultiProfileUtils.errorMessage(
                                context = requireContext(),
                                message = message
                            )
                        )

                    }

                    else -> {}
                }
                navigateToLoginWithParams(
                    navigationId = navigationId,
                    isPopupToBeforeLogin = true,
                    requestRestartApp = requestRestartApp,
                    requestFromOnBoarding = isSelectionOnBoarding,
                    extendsArgs = bundleOf(
                        "targetScreen" to StartingTargetScreenType.SelectProfile
                    )
                )
            }

            is MultiProfileViewModel.MultiProfileState.Error -> {
                when (intent) {
                    is MultiProfileViewModel.MultiProfileIntent.GetListProfile -> {
                        showPageError(
                            title = title, errorMessage = MultiProfileUtils.errorMessage(
                                context = requireContext(),
                                message = message
                            )
                        )

                    }

                    is MultiProfileViewModel.MultiProfileIntent.GetProfileDetail,
                    is MultiProfileViewModel.MultiProfileIntent.GetProfileDetailAndUpdateUserInfo,
                    is MultiProfileViewModel.MultiProfileIntent.LoginProfile -> {
                        binding.root.showSnackBar(
                            title = MultiProfileUtils.errorMessage(
                                context = requireContext(),
                                message = message
                            )
                        )
                    }

                    else -> {}
                }
            }

            is MultiProfileViewModel.MultiProfileState.ResultListUserProfile -> {
                if (data.isSuccess()) {
                    val profileAndMeta = data.data
                    if (profileAndMeta.metadata.title.isNotBlank()) {
                        title = profileAndMeta.metadata.title
                        viewModel.titleUpdateProfile = profileAndMeta.metadata.titleUpdateProfile
                        setToolbarEdit()
                    }
                    processListProfile(data.data.listProfile)
                } else {
                    showPageError(
                        title = title, errorMessage = MultiProfileUtils.errorMessage(
                            context = requireContext(),
                            message = data.message
                        )
                    )
                }

            }

            is MultiProfileViewModel.MultiProfileState.ResultLoginProfile -> {
                processResultLoginProfile(data, pinScreenSource)

            }

            is MultiProfileViewModel.MultiProfileState.ResultGetProfileDetail -> {
                processProfileDetail(data, targetScreen)
            }

            is MultiProfileViewModel.MultiProfileState.Done -> {
                if (intent is MultiProfileViewModel.MultiProfileIntent.GetListProfile ||
                    intent is MultiProfileViewModel.MultiProfileIntent.LoginProfile ||
                    intent is MultiProfileViewModel.MultiProfileIntent.GetProfileDetail ||
                    intent is MultiProfileViewModel.MultiProfileIntent.GetProfileDetailAndUpdateUserInfo
                ) {
                    hideLoading()
                }
            }

            else -> {}
        }
    }


    override fun backHandler() {
        if (isSelectionOnBoarding) return
        Timber.tag("tam-multiProfile").e("send  PROFILE_TYPE_CHANGED_EVENT backhandler  ")
        MultiProfileUtils.sendEventProfileChangedIfChange(
            fragment = this,
            sharedPreferences = sharedPreferences,
            sourceChange = "${this.javaClass.simpleName} - backhandler",
            oldProfileId = oldId,
            oldProfileType = oldType
        )
//        setFragmentResult(Utils.PROFILE_CHANGED_EVENT,
//            bundleOf(
//                Utils.PROFILE_CHANGE_SOURCE to "${this.javaClass.simpleName} - backhandler",
//                Utils.PROFILE_TYPE_OLD to oldType,
//                Utils.PROFILE_TYPE_NEW to sharedPreferences.profileType()
//            )
//        )
        findNavController().navigateUp()
    }


    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        val orientation = MainApplication.INSTANCE.resources.configuration.orientation
        if (lastOrientation == orientation) return

        lastOrientation = orientation ?: Configuration.ORIENTATION_UNDEFINED
        updateRecyclerViewContent(orientation = orientation, init = false)

    }

    private fun updateRecyclerViewContent(orientation: Int, init: Boolean) {
        binding.rvProfile.apply {
            val spanCount = getSpanCount(orientation)
            if (init) {
                layoutManager =
                    FlexBoxLayoutManagerWithLimit(context, FlexDirection.ROW, FlexWrap.WRAP).also {
                        it.maxPerRow = spanCount
                        it.justifyContent = JustifyContent.CENTER
//                it.alignItems = AlignItems.CENTER
                    }
            } else {
                (layoutManager as? FlexBoxLayoutManagerWithLimit)?.maxPerRow = spanCount
                itemDecoration?.let {
                    removeItemDecoration(it)
                }
            }

            getItemDecoration(orientation, spanCount).apply {
                addItemDecoration(this)
                itemDecoration = this
            }
        }


    }

    private fun getSpanCount(orientation: Int): Int {
        return if (context.isTablet()) {
            if (orientation == Configuration.ORIENTATION_PORTRAIT) {
                itemInRow
            } else {
                itemInRowLandscapeTablet
            }
        } else itemInRow
    }

    private fun getItemDecoration(orientation: Int, itemInRow: Int): RecyclerView.ItemDecoration {
        return object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                val position = parent.getChildAdapterPosition(view)
                if (position % itemInRow != 0) {
                    outRect.left =
                        resources.getDimensionPixelSize(R.dimen.multi_profile_profile_item_margin_horizontal_between)
                }
                outRect.bottom =
                    resources.getDimensionPixelSize(R.dimen.multi_profile_profile_item_margin_vertical_between)
            }
        }

    }


    private fun processListProfile(data: List<Profile>) {
        if (data.isEmpty()) {
            showPageError(
                title = title,
                errorMessage = getString(R.string.multi_profile_login_profile_error)
            )
        } else {
            val listProfile = arrayListOf<ProfileItem>()
            var selectedProfileIndex = 0
            data.forEachIndexed { index, profile ->
                listProfile.add(profile.copy().apply { selected = false })
                if (sharedPreferences.profileId() == profile.id) {
                    selectedProfileIndex = index
                }
            }
            (listProfile[selectedProfileIndex] as? Profile)?.let {
                if (!isSelectionOnBoarding) {
                    it.selected = true
                    MultiProfileUtils.saveCurrentProfile(sharedPreferences, it)
                }
            }

            if (isSelectionOnBoarding) {
                if (listProfile.size < maxProfile &&
                    // profile kid can add new profile if selectionOnBoarding is true
                    !listProfile.contains(ProfileItem.AddProfile)
                ) {
                    listProfile.add(ProfileItem.AddProfile)
                }
            } else {
                if (listProfile.size < maxProfile &&
                    !MultiProfileUtils.isProfileKid(sharedPreferences.profileType()) &&        // profile kid can't add new profile
                    !listProfile.contains(ProfileItem.AddProfile)
                ) {
                    listProfile.add(ProfileItem.AddProfile)
                }
            }
            multiProfileAdapter.bind(listProfile)
        }

    }


    // region select profile
    private fun selectProfile(pos: Int, newProfile: Profile, targetScreen: String) {
        // choose current profile TO LOGIN, back to page
        if (!isSelectionOnBoarding) {
            if (targetScreen == LOGIN_PROFILE_TARGET_SCREEN && newProfile.id == sharedPreferences.profileId()) {
                backHandler()
                return
            }
        }

        if (targetScreen == EDIT_PROFILE_TARGET_SCREEN && !newProfile.allowEdit) {
            // choose profile NOT allow to edit, do nothing
            return
        }
        val intent = when (targetScreen) {
            LOGIN_PROFILE_TARGET_SCREEN -> {
                MultiProfileViewModel.MultiProfileIntent.GetProfileDetail(
                    profileId = newProfile.id,
                    targetScreen = targetScreen
                )
            }

            EDIT_PROFILE_TARGET_SCREEN -> {
                MultiProfileViewModel.MultiProfileIntent.GetProfileDetailAndUpdateUserInfo(
                    profileId = newProfile.id,
                    targetScreen = targetScreen
                )
            }

            else -> null
        }
        intent?.let {
            viewModel.dispatchIntent(it)

        }

    }

    private fun processProfileDetail(data: ProfileDetail, targetScreen: String) {
        if (!data.isSuccess()) {
            binding.root.showSnackBar(
                MultiProfileUtils.errorMessage(
                    context = requireContext(),
                    message = data.message
                )
            )
            return
        }
        when (targetScreen) {
            LOGIN_PROFILE_TARGET_SCREEN -> {
                loginProfile(data.data)
            }

            EDIT_PROFILE_TARGET_SCREEN -> {
                editProfile(data.data)
            }
        }


    }

    private fun loginProfile(newProfile: Profile) {
        viewModel.selectedProfile = newProfile
        when (newProfile.pinType) {
            Profile.PinType.NotRequestPin -> switchProfile(newProfile)
            Profile.PinType.ShowPopupWarning -> {
                if (isSelectionOnBoarding) {
                    /***
                     * Note : user is allowed to choose profile kid  switch profile if  on boarding mode
                     * **/
                    switchProfile(newProfile)
                } else {

                    showAlertDialog(
                        title = getString(R.string.multi_profile_login_alert_title),
                        message = newProfile.msgWarningSwitch.ifBlank {
                            getString(
                                R.string.multi_profile_login_alert_message,
                                newProfile.name
                            )
                        },
                        textConfirm = getString(R.string.alert_confirm),
                        textClose = getString(R.string.multi_profile_login_alert_cancel_button),
                        onConfirm = {
                            switchProfile(newProfile)
                        },
                        onClose = {},
                        showTitle = true

                    )
//                findNavController().navigate(
//                    MultiProfileFragmentDirections.actionMultiProfileToPinProfile(
//                        targetScreen = PinProfileScreenType.VerifyPasswordLoginProfile,
//                        title = getString(R.string.multi_profile_password_title),
//                        description = getString(R.string.multi_profile_password_login_profile_des),
//                        profileId = newProfile.id
//                    )
//                )

                }
            }

            Profile.PinType.RequestPrivatePin -> {
                findNavController().navigate(
                    MultiProfileFragmentDirections.actionMultiProfileToPinProfile(
                        targetScreen = PinProfileScreenType.VerifyPinLoginProfile,
                        title = getString(R.string.multi_profile_pin_profile_verify_pin_title),
                        description = getString(R.string.multi_profile_pin_login_profile_des),
                        profileId = newProfile.id,
                        isFromLoginScreen = isFromLoginScreen,
                        isSelectionOnBoarding = isSelectionOnBoarding,
                        popUpToId = R.id.multiProfileFragment,
                        popUpToInclusive = false
                    )
                )

            }

            is Profile.PinType.UnknownType -> {
                showWarningMessageDialog(
                    message = getString(R.string.multi_profile_pin_type_not_supported),
                    textConfirm = getString(R.string.all_string_known)
                )
            }
        }
    }

    private fun processResultLoginProfile(data: LoginProfile, sourceScreen: PinProfileScreenType?) {
        Timber.tag("tam-multiProfile").d("processResultLoginProfile $data")
        if (sourceScreen == null) {
            // request not come from pin screen
            if (!data.isSuccess()) {
                binding.root.showSnackBar(
                    MultiProfileUtils.errorMessage(
                        context = requireContext(),
                        message = data.message
                    )
                )
                return
            }

            MainApplication.INSTANCE.isOpenOnBoardingAndWithProfile = false // update status first open app for onboard
            MultiProfileUtils.saveProfileSelectionShownTime(sharedPreferences)
            // logic onboard profile after switch profile
            if (sharedPreferences.getEnableProfileOnboarding() == "1" && data.data.statusOnboarding == "0") {
                MultiProfileUtils.switchProfile(
                    activity = activity,
                    sharedPreferences = sharedPreferences,
                    profile = data.data,
                    restartAppAfterSwitch = false,
                    restartKeepIntent = isSelectionOnBoarding
                )
                findNavController().navigate(
                    MultiProfileFragmentDirections.actionMultiProfileToOnboardingProfile(
                        profileId = data.data.id,
                        isFromLoginScreen = isFromLoginScreen
                    )
                )
            } else {
                if (isFromLoginScreen) {
                    // logic onboarding login profile
                    /**
                     * save profile before check module Current support profile
                     * **/
                    MultiProfileUtils.sendEventProfileOnboardingIfSwitchProfile(
                        activity = activity,
                        profile = data.data,
                        fragment = this,
                        sharedPreferences = sharedPreferences,
                        sourceChange = "${this.javaClass.simpleName} - backhandler",
                        oldProfileId = oldId,
                        oldProfileType = oldType
                    )

                    if (!checkAppModuleSupportKids.isCheckModuleCurrentSupportKids(
                            isFromLoginScreen = isFromLoginScreen,
                            navigationId = navigationId,
                            profile = data.data,
                            previousBackStackEntryId = previousBackStackEntryId
                        )
                    ) {
                        /**
                         * warning user if kid not support
                         * **/
                        showWarningDialog(
                            textTitle = getString(R.string.multi_profile_kid_not_supported_title),
                            message = getString(R.string.multi_profile_kid_not_supported_feature),
                            textConfirm = getString(R.string.close),
                            isConfirmEnable = true,
                            isExitEnable = true,
                            isCancelOutside = true,
                        )

                    } else {
                        /**
                         * Note : callback to login profile success for Login Fragment
                         * **/
                        MultiProfileUtils.sendEventProfileChangedIfChange(
                            fragment = this,
                            sharedPreferences = sharedPreferences,
                            sourceChange = "${this.javaClass.simpleName} - backhandler",
                            oldProfileId = oldId,
                            oldProfileType = oldType
                        )
                        findNavController().previousBackStackEntry?.arguments?.putBoolean("isLoginProfileChanged", true)
                        findNavController().navigateUp()
                    }
                } else {
                    MultiProfileUtils.switchProfile(
                        activity = activity,
                        sharedPreferences = sharedPreferences,
                        profile = data.data,
                        restartAppAfterSwitch = true,
                        restartKeepIntent = isSelectionOnBoarding
                    )
                }
            }
        }
    }

    private fun switchProfile(newProfile: Profile) {
        viewModel.dispatchIntent(MultiProfileViewModel.MultiProfileIntent.LoginProfile(profileId = newProfile.id))
        multiProfileAdapter.selectProfile(newProfile)
    }
    // endregion select profile

    // region edit profile

    private fun setToolbarEdit() {
        /**
         * Note : OnBoarding will not show edit profile otherwise show all
         * **/
        binding.toolbar.title = title
//        if (isSelectionOnBoarding) binding.toolbar.menu.removeItem(R.id.action_edit_profile)
        if (isSelectionOnBoarding || MultiProfileUtils.isProfileKid(sharedPreferences.profileType())) binding.btnManageProfile.gone()
        else binding.btnManageProfile.show()
        val menuItem = binding.toolbar.menu.findItem(R.id.action_edit_profile)
        binding.toolbar.setNavigationIcon(if(isSelectionOnBoarding) null else ContextCompat.getDrawable(requireContext(), R.drawable.ic_arrow_left))
//        if (inEditMode) {
//            menuItem?.title = if(isSelectionOnBoarding) "" else context?.getString(R.string.done)
//            binding.toolbar.title = viewModel.titleUpdateProfile.ifBlank {
//                getString(R.string.multi_profile_edit_profile_title)
//            }
//
//        } else {
//            menuItem?.title = if(isSelectionOnBoarding) "" else context?.getString(R.string.multi_profile_action_edit_profile_text)
//            binding.toolbar.title = title.ifBlank {
//                getString(R.string.multi_profile_choose_profile_title)
//            }
//        }
    }

    private fun switchEditMode() {
        inEditMode = !inEditMode
    }

    private fun editProfile(profile: Profile) {
        if (!profile.allowEdit) {
            return
        }
        viewModel.selectedProfile = profile
        if (profile.id == sharedPreferences.profileId()) {
            // choose current profile, navigate straight to edit profile layout
            findNavController().navigate(
                MultiProfileFragmentDirections.actionMultiProfileToEditProfile(
                    targetScreen = EditProfileScreenType.EditProfile,
                    profileId = profile.id
                )
            )

            return
        }

        fun navigateToPinSettingFun() {
            findNavController().navigate(
                MultiProfileFragmentDirections.actionMultiProfileToPinProfile(
                    targetScreen = PinProfileScreenType.VerifyPasswordEditProfile,
                    title = getString(R.string.multi_profile_password_title),
                    description = getString(R.string.multi_profile_password_edit_profile_des),
                    profileId = profile.id,
                    isFromLoginScreen = isFromLoginScreen,
                    isSelectionOnBoarding = isSelectionOnBoarding
                )
            )
        }

        fun navigateToCreatePinSettingFun() {
//            MultiProfileVerifyOtpDialogDirections.actionProfileVerifyOtpDialogToProfileResetPasswordDialog(
//                targetScreen = safeArgs.targetScreen,
//                verifyToken = data.verifyToken
//            )

            findNavController().navigate(
                MultiProfileFragmentDirections.actionMultiProfileToProfileResetPasswordDialog(
                    createPinSetting = true,
                    targetScreen = ResetPasswordScreenTargetType.EditProfile,
                    verifyToken = "",
                    popUpToId = R.id.multiProfileFragment,
                    popUpToInclusive = false
                )
            )

        }

        if (sharedPreferences.allowPin()) {
            showAlertDialog(
                title = getString(R.string.f_id_create_pin_setting_title),
                message = getString(R.string.f_id_create_pin_setting_description),
                textConfirm = getString(R.string.f_id_create_pin_setting_confirm_text),
                textClose = getString(R.string.f_id_create_pin_setting_close_text),
                onConfirm = {
                    navigateToCreatePinSettingFun()
                    sharedPreferences.setShouldShowPopUpPin(sharedPreferences.userId(), false)
                },
                isCancelled = true,
                showTitle = true
            )
        } else {

            if (sharedPreferences.shouldShowPopUpPin(sharedPreferences.userId())) {
                sharedPreferences.setShouldShowPopUpPin(sharedPreferences.userId(), false)
                showWarningMessageDialog(
                    title = getString(R.string.f_id_update_pin_setting_title),
                    message = getString(R.string.f_id_update_pin_setting_description),
                    textConfirm = getString(R.string.mini_app_alert_dialog_error_confirm_text),
                    onConfirm = {
                        navigateToPinSettingFun()
                    }
                )
            } else {
                navigateToPinSettingFun()
            }
        }

    }
    // endregion edit profile

    private fun addProfile() {
        viewModel.selectedProfile = Profile()
        findNavController().navigate(
            MultiProfileFragmentDirections.actionMultiProfileToEditProfile(
                targetScreen = EditProfileScreenType.CreateProfile,
                profileId = ""
            )
        )

    }


    companion object {
        const val LOGIN_PROFILE_TARGET_SCREEN = "LoginProfile"
        const val EDIT_PROFILE_TARGET_SCREEN = "EditProfile"
        const val NONE = "Nowhere"
    }
}