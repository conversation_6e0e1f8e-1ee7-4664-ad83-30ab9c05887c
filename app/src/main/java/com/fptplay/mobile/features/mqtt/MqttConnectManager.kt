package com.fptplay.mobile.features.mqtt

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkRequest
import androidx.lifecycle.MutableLiveData
import com.fpl.plugin.mqtt.Action
import com.fpl.plugin.mqtt.Connection
import com.fpl.plugin.mqtt.listener.MqttActionCallback
import com.fpl.plugin.mqtt.model.ReceivedMessage
import com.fpl.plugin.mqtt.model.Subscription
import com.fpl.plugin.mqtt.sevice.QoS
import com.fptplay.mobile.MainApplication
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.mqtt.MqttUtil.EVENT_ERROR
import com.fptplay.mobile.features.mqtt.MqttUtil.LOG_ID_ERROR
import com.fptplay.mobile.features.mqtt.MqttUtil.LWT_MESSAGE
import com.fptplay.mobile.features.mqtt.MqttUtil.LWT_TOPIC
import com.fptplay.mobile.features.mqtt.MqttUtil.PING_CCU_TOPIC
import com.fptplay.mobile.features.mqtt.MqttUtil.PUBLISHER
import com.fptplay.mobile.features.mqtt.MqttUtil.REMOTE_TOPIC
import com.fptplay.mobile.features.mqtt.MqttUtil.SEPARATOR
import com.fptplay.mobile.features.mqtt.MqttUtil.SUBSCRIBER
import com.fptplay.mobile.features.mqtt.MqttUtil.fromJsonString
import com.fptplay.mobile.features.mqtt.MqttUtil.getReconnectInterval
import com.fptplay.mobile.features.mqtt.MqttUtil.isAutoReconnect
import com.fptplay.mobile.features.mqtt.MqttUtil.toJsonString
import com.fptplay.mobile.features.mqtt.MqttUtil.toMillisecond
import com.fptplay.mobile.features.mqtt.model.Publisher
import com.google.gson.Gson
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.domain.Result
import com.xhbadxx.projects.module.domain.entity.fplay.common.MQTTConfig
import com.xhbadxx.projects.module.domain.repository.fplay.CommonRepository
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.eclipse.paho.client.mqttv3.android.IMqttToken
import org.eclipse.paho.client.mqttv3.android.MqttConnectOptions
import org.eclipse.paho.client.mqttv3.android.MqttException
import org.eclipse.paho.client.mqttv3.android.MqttMessage
import timber.log.Timber
import java.util.Timer
import java.util.TimerTask

class MqttConnectManager(
    private val context: Context,
    private val sharedPreferences: SharedPreferences,
    private val commonRepository: CommonRepository,
    private val trackingProxy: TrackingProxy,
    private val trackingInfo: Infor
) {
    companion object {
        val INSTANCE: MqttConnectManager by lazy {
            MqttConnectManager(
                context = MainApplication.INSTANCE.applicationContext,
                sharedPreferences = MainApplication.INSTANCE.sharedPreferences,
                commonRepository = MainApplication.INSTANCE.commonRepository,
                trackingProxy = MainApplication.INSTANCE.trackingProxy,
                trackingInfo = MainApplication.INSTANCE.trackingInfo
            )
        }
        const val LOG_MQTT = "MqttConnectManager"

    }

    private var mqttConnection : Connection? = null
    private var config : MQTTConfig.Data? = null
    private var userTopic: String = ""
    private val connectivityManager by lazy {
        MainApplication.INSTANCE.applicationContext.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private var accessTokenTimer : Timer? = null
    private var timeDelay: Long = 0
    private var retryInterval: Pair<Long,Long>? = null
    private var isScheduled : Boolean = false
    inner class AccessTokenTask : TimerTask() {
        override fun run() {
            getMqttAccessToken()
            isScheduled = false
            accessTokenTimer?.cancel() // Hủy timer sau khi chạy một lần
            accessTokenTimer = null
        }
    }

    fun init() {
        getRetryIntervalForTimer()
        runAccessTokenTimer()
        registerNetworkCallback(object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                super.onAvailable(network)
                Timber.tag(LOG_MQTT).d("Network onAvailable")
                mqttConnection?.connect(true)
            }

            override fun onLost(network: Network) {
                super.onLost(network)
                Timber.tag(LOG_MQTT).d("Network onLost")
            }
        })
    }

    private fun runAccessTokenTimer() {
        if(!isScheduled) {
            if (accessTokenTimer == null) {
                accessTokenTimer = Timer("getAccessTokenTimer", false)
            }
            isScheduled = true
            accessTokenTimer?.schedule(AccessTokenTask(),timeDelay)
        } else {
            Timber.tag(LOG_MQTT).d("The timer for get access token is scheduled or running.")
        }
    }

    private fun getRetryIntervalForTimer() {
        retryInterval = Pair(
            MainApplication.INSTANCE.appConfig.minRetryInterval.toMillisecond(),
            MainApplication.INSTANCE.appConfig.maxRetryInterval.toMillisecond()
        )
    }

    fun registerNetworkCallback(networkCallback: ConnectivityManager.NetworkCallback) {
        connectivityManager.registerNetworkCallback(NetworkRequest.Builder().build(), networkCallback)
    }

    fun unregisterNetworkCallback(networkCallback: ConnectivityManager.NetworkCallback) {
        connectivityManager.unregisterNetworkCallback(networkCallback)
    }

    private fun connectMQTT(successData: MQTTConfig.Data) {
        config = successData
        val clientId = MainApplication.INSTANCE.appConfig.xAgent.plus("__").plus(Util.deviceId(context))
        mqttConnection?.let {
            it.disconnect()
            updateAndConnectMQTT(it, successData, clientId)
        } ?: kotlin.run {
            Timber.tag(LOG_MQTT).i("connectMQTT createConnection")
            mqttConnection = Connection.createConnection(
                clientHandle = "",
                clientId = clientId,
                uri = successData.url.tcp,
                context = context,
                tlsConnection = successData.option.enableTls,
                automaticReconnect = successData.option.automaticReconnect.isAutoReconnect(),
                reconnectInterval = successData.option.automaticReconnect.getReconnectInterval(),
                mqttActionCallback = mqttActionCallback,
                connectionOptions = optionsFromConfig(successData)
            ).connect()
        }
    }

    private fun updateAndConnectMQTT(mqttConnection : Connection, successData : MQTTConfig.Data, clientId : String) {
        Timber.tag(LOG_MQTT).i("connectMQTT updateAndConnectMQTT")
        mqttConnection.updateConnection(
            clientId = clientId,
            uri = successData.url.tcp,
            tlsConnection = successData.option.enableTls,
            automaticReconnect = successData.option.automaticReconnect.isAutoReconnect(),
            reconnectInterval = successData.option.automaticReconnect.getReconnectInterval(),
            mqttActionCallback = mqttActionCallback,
            connectionOptions = optionsFromConfig(successData)
        ).connect(true)
    }

    private fun optionsFromConfig(data: MQTTConfig.Data): MqttConnectOptions {
        val connOpts = MqttConnectOptions()
        connOpts.isCleanSession = false
        connOpts.connectionTimeout = data.option.connectionTimeout
        connOpts.keepAliveInterval = data.option.keepAlive
        connOpts.userName = if (data.jwtFrom == "username") {
            data.token
        } else ""
        connOpts.password = if (data.jwtFrom == "password") {
            data.token.toCharArray()
        } else charArrayOf()

        connOpts.setWill(LWT_TOPIC, LWT_MESSAGE.toByteArray(), data.option.qos, false)

        connOpts.isAutomaticReconnect = false
//           if (data.option.enableTls){
//               // TODO Add Keys to conOpts here
////               connOpts.setSocketFactory()
//           }
        return connOpts
    }

    fun getMessage(payload: String, qos: Int, retained: Boolean) : MqttMessage {
        val mes = MqttMessage(payload.toByteArray())
        mes.qos = qos
        mes.isRetained = retained
        return mes
    }

    private val mqttActionCallback = object : MqttActionCallback {
        override fun onSuccess(action: Action, topic: String, token: IMqttToken?) {
            when(action) {
                Action.CONNECT -> {
                    subscriptionToUserTopic()
                    republishMessagesFailed()
                }
                else -> {}
            }
        }

        override fun requestToken() {
            Timber.tag(LOG_MQTT).i("requestToken")
            if (NetworkUtils.isNetworkAvailable()) {
                getMqttAccessToken()
            }
        }

        override fun onFailure(
            action: Action,
            topic: String,
            token: IMqttToken?,
            exception: Throwable?
        ) {
            when(action) {
                Action.CONNECT -> {
                    if (exception is MqttException) {
                        if (exception.reasonCode == MqttException.REASON_CODE_NOT_AUTHORIZED.toInt()) {
                            sendLogError(
                                code = exception.reasonCode.toString(),
                                msg = exception.cause?.message ?: exception.message ?: ""
                            )
                        }
                    }
                }

                Action.SUBSCRIBE -> {
                    sendLogError(
                        itemName = SUBSCRIBER,
                        url = topic,
                        code = ((exception as? MqttException)?.reasonCode ?: -1).toString(),
                        msg = exception?.cause?.message ?: exception?.message ?: ""
                    )
                }

                Action.PUBLISH -> {
                    sendLogError(
                        itemName = PUBLISHER,
                        url = topic,
                        code = ((exception as? MqttException)?.reasonCode ?: -1).toString(),
                        msg = exception?.cause?.message ?: exception?.message ?: ""
                    )
                }
                else -> {}
            }
        }


    }

    private fun republishMessagesFailed() {
        mqttConnection?.let { connection ->
            val messageFailures = connection.messageList.filter { !it.isSuccess }
            if (messageFailures.isNotEmpty()) {
                messageFailures.forEach {
                    val newMessage = it.message
                    val publisher = newMessage.payload.decodeToString().fromJsonString()
                    if (publisher != null) {
                        publisher.isRetry = 1
                        newMessage.payload = publisher.toJsonString().toByteArray()
                        Timber.tag(Connection.TAG).i("Auto-ReceivedMessage to: ${it.topic} message: $publisher")
                        connection.publishMessage(it.topic, newMessage)
                    } else {
                        Timber.tag(Connection.TAG).i("Auto-ReceivedMessage Failed message is not a valid topic: ${it.topic} message: ${newMessage.payload.decodeToString()}")
                    }
                }
            }
        }
    }

    fun subscriptionToUserTopic() {
        if (userTopic.isNotBlank()) {
            unSubscriberToUserTopic()
        }
        if (sharedPreferences.userLogin()) {
            userTopic = getUserTopic()
            mqttConnection?.addNewSubscription(
                Subscription(
                    topic = userTopic,
                    qos = QoS.valueOf(config?.option?.qos ?: 2),
                    clientHandle = mqttConnection!!.handle(),
                    isEnableNotifications = false
                )
            )
        }
    }

    fun unSubscriberToUserTopic() {
        mqttConnection?.unsubscribe(userTopic)
        userTopic = ""
    }

    fun subscriptionToTopic(type: String, typeId: String) {
        val topic = getContentTopic(type, typeId)
        mqttConnection?.addNewSubscription(
            Subscription(
                topic = topic,
                qos = QoS.valueOf(config?.option?.qos ?: 2),
                clientHandle = mqttConnection!!.handle(),
                isEnableNotifications = false
            )
        )
    }

    fun unSubscriberToTopic(type: String, typeId: String) {
        val topic = getContentTopic(type, typeId)
        mqttConnection?.unsubscribe(topic)
    }

    fun publishToTopic(publisher: Publisher, type: String, typeId: String) {
        Timber.tag(LOG_MQTT).d("publishToTopic publisher: $publisher ")
        val topic = getContentTopic(type, typeId)
        mqttConnection?.publishMessage(
            topic = topic,
            payload = publisher.toJsonString(),
            qos = QoS.valueOf(config?.option?.qos ?: 2),
            isRetained = true
        )
    }

    private fun getUserTopic(): String {
        return StringBuilder()
            .append(REMOTE_TOPIC)
            .append(SEPARATOR)
            .append(sharedPreferences.userId())
            .append(SEPARATOR)
            .append(Util.deviceId(context))
            .toString()
    }

    private fun getContentTopic(type: String, typeId: String): String {
        return StringBuilder()
            .append(PING_CCU_TOPIC)
            .append(SEPARATOR)
            .append(type)
            .append(SEPARATOR)
            .append(typeId)
            .toString()
    }

    fun messageArrived(): MutableLiveData<MutableList<ReceivedMessage>>? {
        return mqttConnection?.messages
    }

    private fun getMqttAccessToken() {
        CoroutineScope(Dispatchers.IO).launch {
            commonRepository.getMQTTConfig().collect {
                when (it) {
                    is Result.Success -> {
                        Timber.tag(LOG_MQTT).d("get MQTT Config Success ${it.successData.data}")
                        if (it.successData.status == "1") {
                            timeDelay = 0
                            connectMQTT(it.successData.data)
                        } else {
                            Timber.tag(LOG_MQTT).d("get MQTT Config Failed Code: ${it.successData.errorCode} ${it.successData.msg}")
                            retryGetAccessToken()
                        }
                    }

                    is Result.Error -> {
                        Timber.tag(LOG_MQTT).d("get MQTT Config Failed ${it.message}")
                        retryGetAccessToken()
                    }

                    else -> {}
                }
            }
        }
    }

    private fun retryGetAccessToken() {
        retryInterval?.let {
            if (it.first > 0 && it.second > 0 && it.first < it.second) {
                if (timeDelay == 0L) {
                    timeDelay = it.first
                } else {
                    timeDelay *= 2
                    if (timeDelay > it.second) {
                        timeDelay = it.second
                    }
                }
                Timber.tag(LOG_MQTT).d("retry Get Access Token after $timeDelay ms")
                runAccessTokenTimer()
            }
        }
    }

    fun sendLogLimitCCU(publisher: Publisher, topic: String) {
        Timber.tag(LOG_MQTT).d("Limit CCU from topic: $topic data: $publisher")
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = LOG_ID_ERROR,
                event = EVENT_ERROR,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = MqttUtil.ACTION_LIMIT_CCU,
                itemName = "",
                url = topic,
                errorCode = publisher.data?.code ?: "",
                errorMessage = publisher.data?.message?.description ?: publisher.data?.message?.title ?: ""
            )
        )
    }

    fun sendLogError(itemName: String = "", url: String = "", screen: String = "", code: String = "", msg: String) {
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo,
                logId = LOG_ID_ERROR,
                event = EVENT_ERROR,
                appId = TrackingUtil.currentAppId,
                appName = TrackingUtil.currentAppName,
                screen = screen,
                itemName = itemName,
                url = url,
                errorCode = code,
                errorMessage = msg
            )
        )
    }
}
