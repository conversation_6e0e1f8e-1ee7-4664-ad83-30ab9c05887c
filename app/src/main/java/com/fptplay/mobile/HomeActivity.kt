package com.fptplay.mobile
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.KeyEvent
import androidx.activity.viewModels
import androidx.annotation.RequiresApi
import androidx.fragment.app.FragmentContainerView
import androidx.lifecycle.MutableLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.navigation.fragment.NavHostFragment
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustDeeplink
import com.fplay.module.downloader.VideoDownloadManager
import com.fplay.module.downloader.listener.DownloadKeyResult
import com.fplay.module.downloader.listener.DownloadListener
import com.fplay.module.downloader.listener.IDownloadKeyListener
import com.fplay.module.downloader.model.TaskInitialStatus
import com.fplay.module.downloader.model.VideoTaskItem
import com.fplay.module.downloader.model.VideoTaskState
import com.fptplay.mobile.common.extensions.isHostFirebaseFPlay
import com.fptplay.dial.connection.FConnectionManager
import com.fptplay.dial.connection.android_tv.model.FAndroidTVCustomExceptionMessageResponse
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiver
import com.fptplay.dial.connection.models.FAndroidTVObjectReceiverExternal
import com.fptplay.dial.connection.models.FBoxObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiver
import com.fptplay.dial.connection.models.FSamsungObjectReceiverExternal
import com.fptplay.dial.connection.models.ObjectReceiver
import com.fptplay.dial.connection.models.transfer_model.ExceptionEventType
import com.fptplay.dial.connection.models.transfer_model.ReceiveExceptionEvent
import com.fptplay.mobile.common.extensions.ActivityExtensions.findNavHostFragment
import com.fptplay.mobile.common.extensions.ActivityExtensions.isInPiPMode
import com.fptplay.mobile.common.extensions.isHostAdjustTrueLink
import com.fptplay.mobile.common.extensions.isTablet
import com.fptplay.mobile.common.ui.bases.BaseActivity
import com.fptplay.mobile.common.utils.*
import com.fptplay.mobile.common.utils.CheckValidUtil
import com.fptplay.mobile.common.utils.Constants
import com.fptplay.mobile.common.utils.DateTimeUtils
import com.fptplay.mobile.common.utils.DeeplinkConstants
import com.fptplay.mobile.common.utils.DeeplinkLocalUtils
import com.fptplay.mobile.common.utils.DeeplinkUtils
import com.fptplay.mobile.common.utils.KeyEventHelper
import com.fptplay.mobile.common.utils.NetworkUtils
import com.fptplay.mobile.common.utils.PlayerPipEventType
import com.fptplay.mobile.common.utils.TrackingUtil
import com.fptplay.mobile.features.adjust.AdjustAllEvent
import com.fptplay.mobile.features.download.DownloadUtils
import com.fptplay.mobile.features.mega.apps.airline.AirlineActivity
import com.fptplay.mobile.features.mega.apps.airline.model.AirlineBrand
import com.fptplay.mobile.features.multi_profile.utils.ProfileOnBoardingNavHostWrapper
import com.fptplay.mobile.features.pairing_control.CheckPackageUtil
import com.fptplay.mobile.features.pairing_control.PairingControlViewModel
import com.fptplay.mobile.features.pairing_control.Utils
import com.fptplay.mobile.features.pairing_control.Utils.isMyMessage
import com.fptplay.mobile.features.pairing_control.Utils.isSuccess
import com.tear.modules.tracking.TrackingProxy
import com.tear.modules.tracking.model.Infor
import com.tear.modules.tracking.model.InforMobile
import com.xhbadxx.projects.module.util.common.Util
import com.xhbadxx.projects.module.util.fplay.SharedPreferences
import com.xhbadxx.projects.module.util.logger.Logger
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class HomeActivity : BaseActivity(R.layout.home_activity) {

    @Inject
    lateinit var trackingProxy: TrackingProxy
    @Inject
    lateinit var trackingInfo: Infor
    @Inject
    lateinit var sharedPreferences: SharedPreferences

    override val navHostFragment by lazy { this.findViewById<FragmentContainerView>(R.id.nav_host_fragment).getFragment() as? NavHostFragment }
    private var isSendLogCloseApp = false

    // Pairing control
    private val pairingConnection by lazy { MainApplication.INSTANCE.pairingConnectionHelper}
    private val pairingCheckPackageUtil by lazy { CheckPackageUtil(this, pairingControlViewModel, findNavHostFragment(), true) }
    private val pairingControlViewModel : PairingControlViewModel by viewModels()
    //
    private val pauseDownloadScope = CoroutineScope(Dispatchers.IO)
    private var pauseDownloadJob : Job? = null

    val onKeyDownObserver: MutableLiveData<Int> = MutableLiveData()
    var isFirstDownload = true

    private val navHostProfileOnBoardingWrapper by lazy{
        ProfileOnBoardingNavHostWrapper(
        sharedPrefs = sharedPreferences,
        activity = this
    ) }

    // permission
    private val appPermissionNotificationHelper: AppPermissionNotificationHelper by lazy {
        AppPermissionNotificationHelper(registry = activityResultRegistry, sharedPref = sharedPreferences)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DeeplinkUtils.registerDeeplink(homeActivity = this)
        processDeeplink(intent)
        VideoDownloadManager.instance.setTrackingDownloadListener(mDownloadListener)
        VideoDownloadManager.instance.setDownloadKeyListener(mDownloadKeyListener)

        // Enable rotation for tablet
        enableRotateForTablet()

        // Pairing control
        lifecycle.addObserver(pairingCheckPackageUtil)
        pairingConnection.addCommunicationListener(communicationListener)
        //

        Logger.d("logid oncreate = ${MainApplication.INSTANCE.isReloadHome}")
        if(!MainApplication.INSTANCE.isReloadHome) {
            logStartApp()
        }

        registerNetworkListener()

        //multi profile
        //enter new home (new task & clear task) already so clear flag clear_task
        sharedPreferences.saveProfileChangeWhenOpenApp(false)
        // request permission
        registerPermissionNotificationListener()
    }
    private fun registerNetworkListener() {
        val hasNetwork = NetworkUtils.isNetworkAvailable()
        handleNetworkState(hasNetwork)
        MainApplication.INSTANCE.networkDetector.observe(this) {
            it?.let { hasNetwork ->
                logNetwork(hasNetwork)
                handleNetworkState(hasNetwork)
                val isHasNetwork = if (hasNetwork) NetworkUtils.isNetworkAvailable() else false
                     if (!isInPiPMode()) showSnackBarMessage(
                         iconLocal = getIconNetwork(isHasNetwork),
                         isOnlyTimeShow = true,
                         text = getErrorMessageNetwork(isHasNetwork,sharedPreferences)
                     )
            }
        }
    }

    private fun registerPermissionNotificationListener() {
        lifecycle.addObserver(appPermissionNotificationHelper)
        appPermissionNotificationHelper.setPermissionResultListener(object :
           AppPermissionNotificationHelper.ResultListener{
           override fun onPermissionNotifyResultListener(result: AppPermissionResultNotificationData) {
               val isGranted = result.grantResults.values.all { it }
                if(isGranted){
                    Timber.tag("LogPermissionNotification").d("Accept Permission notification Successfully")
                }else{
                    // case : do nothing
                }
           }
       })
    }

    private fun handleNetworkState(hasNetwork : Boolean) {
        isFirstDownload = MainApplication.INSTANCE.isFirstDownload
        try {
            if (pauseDownloadJob?.isActive == true) {
                pauseDownloadJob?.cancel(CancellationException("Internet is back!"))
            }
        } catch (ex: CancellationException) {
            Timber.tag("LogNetwork").d("${ex}")
        }
        if (hasNetwork) {
            checkNetworkAndDownloadTasks(MainApplication.INSTANCE.appConfig.d2gTime)
        } else {
            pauseDownloadtasks()
        }
    }
    private fun pauseDownloadtasks() {
        pauseDownloadJob = pauseDownloadScope.launch {
            delay(NetworkUtils.NETWORK_TIMEOUT)
            if (VideoDownloadManager.instance.isDownloading()) {
                Timber.tag("LogNetwork").d("pause")
                if (isFirstDownload) {
                    VideoDownloadManager.instance.pauseAllDownloadTasks()
                    MainApplication.INSTANCE.isFirstDownload = false
                } else {
                    VideoDownloadManager.instance.pauseDownloadTasksAndAddToGroup(Constants.GROUP_BY_INTERNET)
                }
            } else {
                MainApplication.INSTANCE.isFirstDownload = false
            }
        }
    }

    private fun checkNetworkAndDownloadTasks(d2gTime : Int) {
        if (VideoDownloadManager.instance.isDownloading()) {
            val networkType = com.fptplay.mobile.common.utils.Utils.checkNetWorkType(this)
            if (!NetworkUtils.isNetworkAvailable() || !NetworkUtils.isWifiEnabled() && networkType != com.fptplay.mobile.common.utils.Utils.NetworkType.MOBILE) {
                pauseDownloadtasks()
                return
            }
            if (sharedPreferences.alwaysDownloadByWifi() && networkType == com.fptplay.mobile.common.utils.Utils.NetworkType.MOBILE) {
                return
            }
            if (isFirstDownload) {
                Timber.tag("LogNetwork").d("reDownload")
                VideoDownloadManager.instance.reDownloadAllDownloadingTask(d2gTime, TaskInitialStatus.REOPENAPP)
                MainApplication.INSTANCE.isFirstDownload = false
            } else {
                Timber.tag("LogNetwork").d("resume")
                VideoDownloadManager.instance.resumeDownloadTasksInGroup(Constants.GROUP_BY_INTERNET)
            }
        } else {
            MainApplication.INSTANCE.isFirstDownload = false
        }
    }
    private fun logNetwork(hasNetwork: Boolean) {
        val networkType = com.fptplay.mobile.common.utils.Utils.checkNetWorkType(this)
        Timber.tag("LogNetwork").d("hasNetwork: ${hasNetwork} || isNetworkAvailable: ${NetworkUtils.isNetworkAvailable()} || isWifiEnabled: ${NetworkUtils.isWifiEnabled()} || networkType: $networkType")
    }

    private fun logStartApp(){
        Timber.d("*****LogStartApp with device: ${Util.deviceId(this)}")
        trackingProxy.sendEvent(
            InforMobile(
                infor = trackingInfo, logId = "13", appId = TrackingUtil.currentAppId, appName = TrackingUtil.currentAppName,
                screen = "StartApplication", event = "StartApplication",
                boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                itemName = sharedPreferences.userPhone()
            )
        )

        if(NetworkUtils.isNetworkAvailable()){
            sendLogOffline()
        }
    }
    private fun sendLogOffline(){
        val data = arrayListOf<InforMobile>()
        data.addAll(TrackingUtil.arrayLogOffline)
        data.forEach { info->
            trackingProxy.sendEvent(info)
        }
        TrackingUtil.arrayLogOffline.clear()
        sharedPreferences.setLogD2G("")
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        processDeeplink(intent)
    }

    override fun onStart() {
        super.onStart()
        registerAdjustBroadcastReceiver()
    }

    override fun onResume() {
        super.onResume()
        // Pairing Control
        triggerRetryPairingConnectWhenLoss()
        // request permission
        appPermissionNotificationHelper.askNotificationPermission(activity = this@HomeActivity)
        navHostProfileOnBoardingWrapper.switchToOnBoardingNavGraph()

    }

    override fun onStop() {
        if(isFinishing) {
            logCloseApp()
        }
        unregisterAdjustBroadcastReceiver()
        super.onStop()
    }

    override fun onDestroy() {
        if(!isSendLogCloseApp) {
            logCloseApp()
        }
        DeeplinkUtils.unregisterDeeplink(this)
        MainApplication.INSTANCE.networkDetector.removeObservers(this)
        // Pairing control
        lifecycle.removeObserver(pairingCheckPackageUtil)
        //Permission Notification
        lifecycle.removeObserver(appPermissionNotificationHelper)
        pairingConnection.removeCommunicationListener(communicationListener)
        //
        super.onDestroy()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        onKeyDownObserver.postValue(keyCode)
        return if (KeyEventHelper.getInstance().isHandleKeyManual()) {
            if (KeyEventHelper.getInstance().handleOnKeyDown(keyCode = keyCode, event = event)) {
                true
            } else {
                super.onKeyDown(keyCode, event)
            }
        } else {
            super.onKeyDown(keyCode, event)
        }
    }

    private fun logCloseApp() {
        isSendLogCloseApp = true
        if(!MainApplication.INSTANCE.isReloadHome) {
            trackingProxy.sendEvent(
                InforMobile(
                    infor = trackingInfo, logId = "12", appId = TrackingUtil.currentAppId, appName = TrackingUtil.currentAppName,
                    event = "CloseApplication",
                    boxTime = DateTimeUtils.getLogSessionAtCurrentTime()
                )
            )
            TrackingUtil.resetDataLogWhenStopApp()
        }
        MainApplication.INSTANCE.isReloadHome = false
        Logger.d("logid logCloseApp = reset profile")
    }

    // region deeplink
    private fun processDeeplink(intent: Intent?) {
        Timber.e("processDeeplink $intent")
        if(intent?.hasExtra(DeeplinkConstants.REQUIRE_RESUME_ACTIVITY) == true) {
            Timber.d("processDeeplink should return ${intent.getBooleanExtra(DeeplinkConstants.REQUIRE_RESUME_ACTIVITY, false)}")
            if(intent.getBooleanExtra(DeeplinkConstants.REQUIRE_RESUME_ACTIVITY, false)) {
                return
            }
        }
        val urlToProcess =
            processFirebaseNotificationDeeplink(homeIntent = intent) ?:
            processFirebaseDynamicDeepLink(homeIntent = intent) ?:
            processAppLinkFacebook(homeIntent = intent) ?:
            processAdjustDeeplink(homeIntent = intent) ?:
            getGeneralDeeplink(homeIntent = intent)


        DeeplinkUtils.parseDeepLinkAndExecute(
            deeplink = urlToProcess ?: "",
            useWebViewInApp = false,
            trackingInfo = trackingInfo,
            isDeeplinkCalledInApp = false
        )
    }

    private fun processFirebaseNotificationDeeplink(homeIntent: Intent?): String? {
        Timber.d("processFirebaseNotificationDeeplink $homeIntent")
        if(homeIntent == null) return null

        return if(homeIntent.hasExtra(Constants.FIREBASE_NOTIFICATION_NEW)) {
            try {
                if (homeIntent.getBooleanExtra(Constants.FIREBASE_NOTIFICATION_NEW, false)) {
                    val type = homeIntent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE) ?: ""
                    val typeId =
                        homeIntent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TYPE_ID) ?: ""
                    val url = homeIntent.getStringExtra(Constants.FIREBASE_NOTIFICATION_URL) ?: ""
                    val title = homeIntent.getStringExtra(Constants.FIREBASE_NOTIFICATION_TITLE)
                        ?.ifBlank { homeIntent.getStringExtra(Constants.FIREBASE_NOTIFICATION_FILED_TITLE) }
                        ?: ""
                    val navHostFragment = navHostFragment
                    val isProcessDeeplinkFromTypeNotification =
                        navHostFragment != null
                                && type.isNotBlank()
                                && typeId.isNotBlank()
                                && DeeplinkLocalUtils.processAndPlayDeepLinksFromTypeNotification(
                                    context = this,
                                    navHostFragment = navHostFragment,
                                    type = type,
                                    contentId = typeId
                                )
                    //log kibana 19
                    var appName = ""
                    if (CheckValidUtil.checkValidString(type) && CheckValidUtil.checkValidString(typeId)){
                        appName = type
                        com.fptplay.mobile.common.utils.Utils.checkAndSaveUTM(deeplink = url, isDeeplinkCalledInApp = false)
                    }else{
                        appName = "Deeplink"
                        com.fptplay.mobile.common.utils.Utils.checkAndSaveUTM(deeplink = url, isDeeplinkCalledInApp = false)
                    }
                    AdjustAllEvent.sendPushNotifyOpenEvent(notificationId = typeId, notificationTitle = title)
                    trackingProxy.sendEvent(
                        InforMobile(infor = trackingInfo,
                            logId = "19",
                            appId = appName,
                            appName = appName,
                            event = "Confirmed",
                            screen = "Ok",
                            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
                            itemId = typeId,
                            itemName = title,

                        ))
                    TrackingUtil.screen = TrackingUtil.screenNotification //set notification for view after

                    if(isProcessDeeplinkFromTypeNotification) {
                        // if already process return blank link so no further action
                        ""
                    } else if(url.isNotBlank()) {
                        url
                    } else {
                        null
                    }
                } else {
                    null
                }
            } catch(e: Exception) {
                Timber.e(e, "processFirebaseNotificationDeeplink")
                null
            }
        } else {
            null
        }
    }

    private fun processFirebaseDynamicDeepLink(homeIntent: Intent?): String? {
        Timber.d("processFirebaseDynamicDeepLink $homeIntent")
        if (homeIntent == null) return null
        return if (homeIntent.getBooleanExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY, false)) {
            try {
                val urlDeepLink =
                    homeIntent.getStringExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_URL_KEY) ?: ""
                Timber.i("urlDeepLink: $urlDeepLink")
                val uriDynamicLink = Uri.parse(urlDeepLink)
                if (uriDynamicLink != null && uriDynamicLink.host?.isHostFirebaseFPlay() == true
                    && !uriDynamicLink.pathSegments.isNullOrEmpty()
                    && uriDynamicLink.pathSegments[0] == "vnairline") {
                    // case vietnam airline
                    val status = uriDynamicLink.getQueryParameter("status")
                    val transactionId = uriDynamicLink.getQueryParameter("transactionId")
                    val validFromDate = uriDynamicLink.getQueryParameter("validFromDate")
                    val validToDate = uriDynamicLink.getQueryParameter("validToDate")
                    val language = uriDynamicLink.getQueryParameter("language")
                    navigateToAirline(AirlineBrand.VN_AIRLINE)
                    // TODO(): VIETNAM AIRLINE LOGIC
//                        getVnAirlineVerifyReservationByDeepLink(
//                            initVnAirlineSaveTransactionBody(
//                                status,
//                                transactionId,
//                                validFromDate,
//                                validToDate,
//                                language
//                            )
//                        )
                    null

                } else if(urlDeepLink.isNotBlank()) {
                    urlDeepLink
                } else {
                    null
                }
            } catch(e: Exception) {
                Timber.e(e, "processFirebaseDynamicDeepLink")
                null
            }
        } else {
            Timber.i("homeIntent.getBooleanExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY, false) == false")

            null
        }
    }

    private fun processAppLinkFacebook(homeIntent: Intent?): String?  {
        Timber.d(" processAppLinkFacebook ${homeIntent?.extras?.toString()}")
        if(homeIntent == null) return null
        return if (homeIntent.getBooleanExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY, false)) {
            val urlAppLink = homeIntent.getStringExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_DATA_KEY) ?: ""
            Timber.i("urlAppLink: $urlAppLink")

//            DeeplinkUtils.parseDeepLinkAndExecute(
//                deeplink = urlAppLink,
//                useWebViewInApp = false,
//                trackingInfo = trackingInfo,
//                isDeeplinkCalledInApp = false
//
//            )
            urlAppLink.ifBlank {
                null
            }
        } else {
            Timber.i("homeIntent.getBooleanExtra(DeeplinkConstants.APP_LINK_FACE_BOOK_NEW_KEY, false) == false")
            null
        }
    }

    private fun processAdjustDeeplink(homeIntent: Intent?): String? {
        Timber.d("HomeActivity >> processAdjustTrueLink ${homeIntent?.data}")
        if (homeIntent == null) return null
        return if (homeIntent.getBooleanExtra(DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY, false)) {
            try {
                val urlDeepLink =
                    homeIntent.getStringExtra(DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY) ?: ""
                Timber.i("urlDeepLink: $urlDeepLink")
                val uriDynamicLink = Uri.parse(urlDeepLink)
                if (uriDynamicLink != null && uriDynamicLink.host?.isHostAdjustTrueLink() == true
                    && !uriDynamicLink.pathSegments.isNullOrEmpty()
                    && uriDynamicLink.pathSegments[0] == "vnairline") {
                    // case vietnam airline
                    val status = uriDynamicLink.getQueryParameter("status")
                    val transactionId = uriDynamicLink.getQueryParameter("transactionId")
                    val validFromDate = uriDynamicLink.getQueryParameter("validFromDate")
                    val validToDate = uriDynamicLink.getQueryParameter("validToDate")
                    val language = uriDynamicLink.getQueryParameter("language")
                    navigateToAirline(AirlineBrand.VN_AIRLINE)
                    // TODO(): VIETNAM AIRLINE LOGIC
//                        getVnAirlineVerifyReservationByDeepLink(
//                            initVnAirlineSaveTransactionBody(
//                                status,
//                                transactionId,
//                                validFromDate,
//                                validToDate,
//                                language
//                            )
//                        )
                    null

                } else if(urlDeepLink.isNotBlank()) {
                    urlDeepLink
                } else {
                    null
                }
            } catch(e: Exception) {
                Timber.e(e, "processFirebaseDynamicDeepLink")
                null
            }
        } else {
            Timber.i("homeIntent.getBooleanExtra(DeeplinkConstants.FIREBASE_DYNAMIC_LINK_NEW_KEY, false) == false")

            null
        }
    }

    private fun getGeneralDeeplink(homeIntent: Intent?): String? {
        if(homeIntent == null) return null
        val extras = homeIntent.extras ?: return null
        return try {
            val originalLink = extras.getString("originalLink")
            Timber.i("originalLink: $originalLink")
            originalLink
        } catch (e: Exception) {
            Timber.e(e, "getGeneralDeeplink")
            null
        }
    }
    // endregion deeplink
    private fun navigateToAirline(airlineBrand: AirlineBrand) {
        val intent = Intent(this, AirlineActivity::class.java)
        intent.putExtra(AirlineActivity.AIRLINE_BRAND_KEY, airlineBrand)
        startActivity(intent)
    }

    //region Pairing Control
    private fun triggerRetryPairingConnectWhenLoss() {
        if (MainApplication.INSTANCE.pairingConnectionHelper.isConnected) {
            val isServiceConnected = MainApplication.INSTANCE.pairingConnectionHelper.isServiceConnected()
            Logger.d("PairingControlConnectionHelper => TriggerRetryPairingConnectWhenLoss => isServiceConnected: $isServiceConnected")
            isServiceConnected?.let { // For Internal TV Samsung Only
                if (!it) {
                    MainApplication.INSTANCE.pairingConnectionHelper.reconnect()
                }
            }
            pairingConnection.sendEventGetStatusWatching(id = pairingConnection.getCastingItem()?.id ?: "", refId = pairingConnection.getCastingItem()?.refId ?: "")
        }
    }

    private val communicationListener = object : FConnectionManager.FCommunicationListener {
        override fun onMessage(message: ObjectReceiver) {
            when (message) {
                is FBoxObjectReceiver -> {
                    when (message.data) {
                        is ReceiveExceptionEvent -> {
                            (message.data as? ReceiveExceptionEvent)?.run {
                                if (result.isSuccess() && senderId.isMyMessage()) {
                                    if (Utils.isSessionRunning()) {
                                        data?.let { data ->
                                            when (data.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    // Clear user data
                                                    if (data.actionType == ExceptionEventType.REQUIRE_LOGIN) {
                                                        com.fptplay.mobile.common.utils.Utils.clearUserData(sharedPreferences)
                                                    }
                                                    // Save processing data
                                                    pairingConnection.saveProcessingData(type = data.type, id = data.id, actionType = data.actionType)
                                                    //
                                                    runOnUiThread {
                                                        pairingCheckPackageUtil.navigateToSelectedContent(
                                                            id = data.id,
                                                            highlightId = data.highlightId,
                                                            type = data.type,
                                                            episodeId = data.episodeId,
                                                            bitrateId = data.bitrateId,
                                                            isPremiere = data.isPremiere
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                is FSamsungObjectReceiver,
                is FSamsungObjectReceiverExternal -> {
                    when (message.data) {
                        is ReceiveExceptionEvent -> {
                            (message.data as? ReceiveExceptionEvent)?.run {
                                if (result.isSuccess() && senderId.isMyMessage()) {
                                    if (Utils.isSessionRunning()) {
                                        data?.let { data ->
                                            when (data.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    // Clear user data
                                                    if (data.actionType == ExceptionEventType.REQUIRE_LOGIN) {
                                                        com.fptplay.mobile.common.utils.Utils.clearUserData(sharedPreferences)
                                                    }
                                                    // Save processing data
                                                    pairingConnection.saveProcessingData(type = data.type, id = data.id, actionType = data.actionType)
                                                    //
                                                    runOnUiThread {
                                                        pairingCheckPackageUtil.navigateToSelectedContent(
                                                            id = data.id,
                                                            highlightId = data.highlightId,
                                                            type = data.type,
                                                            episodeId = data.episodeId,
                                                            bitrateId = data.bitrateId,
                                                            isPremiere = data.isPremiere
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                is FAndroidTVObjectReceiver -> {
                    when (message.data) {
                        is FAndroidTVCustomExceptionMessageResponse -> {
                            (message.data as? FAndroidTVCustomExceptionMessageResponse)?.run {
                                if (result.isSuccess()) {
                                    if (Utils.isSessionRunning()) {
                                        data?.let { data ->
                                            when (data.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    // Clear user data
                                                    if (data.actionType == ExceptionEventType.REQUIRE_LOGIN) {
                                                        com.fptplay.mobile.common.utils.Utils.clearUserData(sharedPreferences)
                                                    }
                                                    // Save processing data
                                                    pairingConnection.saveProcessingData(type = data.type, id = data.id, actionType = data.actionType)
                                                    //

                                                    runOnUiThread {
                                                        pairingCheckPackageUtil.navigateToSelectedContent(
                                                            id = data.id,
                                                            highlightId = data.highlightId,
                                                            type = data.type,
                                                            episodeId = data.episodeId,
                                                            bitrateId = data.bitrateId,
                                                            isPremiere = data.isPremiere
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                is FAndroidTVObjectReceiverExternal -> {
                    when (message.data) {
                        is ReceiveExceptionEvent -> {
                            (message.data as? ReceiveExceptionEvent)?.run {
                                if (result.isSuccess() && senderId.isMyMessage()) {
                                    if (Utils.isSessionRunning()) {
                                        data?.let { data ->
                                            when (data.actionType) {
                                                ExceptionEventType.REQUIRE_LOGIN,
                                                ExceptionEventType.REQUIRE_PAYMENT -> {
                                                    // Clear user data
                                                    if (data.actionType == ExceptionEventType.REQUIRE_LOGIN) {
                                                        com.fptplay.mobile.common.utils.Utils.clearUserData(sharedPreferences)
                                                    }
                                                    // Save processing data
                                                    pairingConnection.saveProcessingData(type = data.type, id = data.id, actionType = data.actionType)
                                                    //

                                                    runOnUiThread {
                                                        pairingCheckPackageUtil.navigateToSelectedContent(
                                                            id = data.id,
                                                            highlightId = data.highlightId,
                                                            type = data.type,
                                                            episodeId = data.episodeId,
                                                            bitrateId = data.bitrateId,
                                                            isPremiere = data.isPremiere
                                                        )
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else -> {}
                    }
                }
                else -> {}
            }
        }
    }
    //endregion

    //log for D2G
    //region Download Video
    private val mDownloadListener: DownloadListener = object : DownloadListener() {
        override fun onDownloadDefault(oldState: Int, item: VideoTaskItem) {
            if (oldState == VideoTaskState.SUCCESS) {
                logDownloadStatus(DownloadUtils.ClearDownloaded, item)
            } else {
                logDownloadStatus(DownloadUtils.CancelDownload, item)
            }
        }

        override fun onDownloadInitial(item: VideoTaskItem) {
            var event = ""
            when(item.initialState) {
                TaskInitialStatus.NEW,
                TaskInitialStatus.RESTART-> {
                    event = DownloadUtils.StartDownload
                }
                TaskInitialStatus.RETRY,
                TaskInitialStatus.REOPENAPP-> {
                    event = DownloadUtils.Resume
                }
            }
            if (event.isNotBlank()) {
                logDownloadStatus(event, item, item.initialState)
            }
        }

        override fun onDownloadError(item: VideoTaskItem) {
            if (!item.hasLinkRefetch) {
                logDownloadStatus(DownloadUtils.DownloadFail, item)
            }
        }

        override fun onDownloadSuccess(item: VideoTaskItem) {
            logDownloadStatus(DownloadUtils.DownloadSuccess, item)
        }

    }

    private val mDownloadKeyListener: IDownloadKeyListener = object: IDownloadKeyListener {
        override fun getKeyOffline(
            item: VideoTaskItem,
            result: DownloadKeyResult
        ) {
            DownloadUtils.getKeyOffId(this@HomeActivity, item, result, trackingProxy, trackingInfo)
        }
    }

    private fun logDownloadStatus(event:String, item: VideoTaskItem, status: String = "") {
        val logInfo = InforMobile(
            infor = trackingInfo,
            logId = "516",
            appSource = item.appId ?: "" ,
            appId = TrackingUtil.currentAppId,
            appName = TrackingUtil.currentAppName,
            screen = TrackingUtil.screen,
            subMenuId = TrackingUtil.blockId,
            event = event,
            status = status,
            itemId = item.movieId,
            itemName = item.title,
            chapterId = item.chapterIdx.toString(),
            EpisodeID = item.episodeId,
            refItemId = item.refItemId ?: "",
            refEpisodeID = item.refEpisodeId ?: "",
            url = item.url,
            isLinkDRM = "0",
            errorCode = item.errorCode.toString(),
            errorMessage = item.errorMessage,
            boxTime = DateTimeUtils.getLogSessionAtCurrentTime(),
            startTime = TrackingUtil.startTime,
            blocKPosition = TrackingUtil.blockIndex,
            isRecommend = TrackingUtil.isRecommend
        )
        Timber.tag("LogDownloadStatus").d("info: $logInfo ")
        trackingProxy.sendEvent(logInfo)
    }

    //region Tablet Rotation
    private fun enableRotateForTablet() {
        if (isTablet()) {
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
    }
    //endregion Tablet Rotation

    //region Picture in Picture
    override fun onUserLeaveHint() {
        sendPiPBroadcast(data = PlayerPipEventType.USER_LEAVE_HINT, fromEvent = true)
        super.onUserLeaveHint()
    }

    override fun onRestart() {
        super.onRestart()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            sendPiPBroadcast(
                data = if (isInPictureInPictureMode) PlayerPipEventType.PIP_OPEN else PlayerPipEventType.PIP_CLOSE,
                fromEvent = false
            )
        }
    }

    @RequiresApi(Build.VERSION_CODES.O)
    override fun onPictureInPictureModeChanged(
        isInPictureInPictureMode: Boolean, newConfig: Configuration
    ) {
        super.onPictureInPictureModeChanged(isInPictureInPictureMode, newConfig)
        sendPiPBroadcast(
            data = if (isInPictureInPictureMode) PlayerPipEventType.PIP_OPEN else PlayerPipEventType.PIP_CLOSE,
            fromEvent = true
        )
    }

    private fun sendPiPBroadcast(data: PlayerPipEventType, fromEvent: Boolean) {
        LocalBroadcastManager.getInstance(this).sendBroadcast(Intent(Constants.PLAYER_PIP_BROADCAST_EVENT).apply {
            putExtra(Constants.PLAYER_PIP_BROADCAST_EVENT_TYPE, data.value)
            putExtra(Constants.PLAYER_PIP_BROADCAST_EVENT_SOURCE, fromEvent)
        })
    }
    //endregion

    private val adjustBroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            Logger.d("HomeActivity >> AdjustBroadcastReceiver onReceive intent: ${intent?.data}")
            intent?.let {
                if (<EMAIL>()?.navController?.currentDestination?.id == R.id.multiProfileFragment) {
                    <EMAIL> { homeIntent ->
                        if (intent.getBooleanExtra(
                                DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                                false
                            )
                        ) {
                            try {
                                val urlDeepLink =
                                    intent.getStringExtra(DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY)
                                        ?: ""
                                if (urlDeepLink.isNotBlank()) {
                                    homeIntent.putExtra(
                                        DeeplinkConstants.ADJUST_TRUE_LINK_NEW_KEY,
                                        true
                                    )
                                    homeIntent.putExtra(
                                        DeeplinkConstants.ADJUST_TRUE_LINK_URL_KEY,
                                        urlDeepLink
                                    )
                                }
                            } catch (ex: Exception) {
                                ex.printStackTrace()
                            }
                        }
                    }
                } else {
                    processDeeplink(intent)
                }
            }
        }
    }

    private fun registerAdjustBroadcastReceiver() {
        val filter = IntentFilter(DeeplinkConstants.ADJUST_TRUE_LINK_BROADCAST_INTENT)
        LocalBroadcastManager.getInstance(MainApplication.INSTANCE.applicationContext).registerReceiver(adjustBroadcastReceiver, filter)
    }

    private fun unregisterAdjustBroadcastReceiver() {
        LocalBroadcastManager.getInstance(MainApplication.INSTANCE.applicationContext).unregisterReceiver(adjustBroadcastReceiver)
    }
}
