<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".features.mega.account.AccountVerifyOtpFragment">
    <com.fptplay.mobile.common.ui.view.CenteredTitleToolbar
        android:id="@+id/toolbar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:navigationIcon="@drawable/ic_arrow_left"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_margin="@dimen/delete_account_otp_layout_margin"
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/layout_verity_otp"
        android:layout_width="@dimen/delete_account_otp_layout_width_"
        android:layout_height="0dp">

        <TextView
            android:id="@+id/tv_input_otp_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/delete_account_input_otp"
            style="@style/OTPTextTitleStyle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />
        <TextView
            android:id="@+id/tv_input_otp_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            tools:text="@string/delete_account_input_otp_des"
            style="@style/OTPTextStyle"
            android:textColor="@color/app_content_text_disable_color"
            android:layout_marginTop="@dimen/delete_account_otp_edit_text_margin_top"
            app:layout_constraintTop_toBottomOf="@id/tv_input_otp_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_input_otp"
            style="@style/CustomEditTextDeleteAccountOTP"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/account_delete_edit_text_otp_state"
            android:hint="@string/delete_account_hint_input_otp"
            android:gravity="center_vertical"
            android:inputType="number"
            android:layout_marginTop="@dimen/delete_account_otp_edit_text_margin_top"
            android:padding="@dimen/delete_account_otp_edit_text_padding"
            app:layout_constraintTop_toBottomOf="@id/tv_input_otp_subtitle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:textCursorDrawable="@drawable/search_cursor_background"
            tools:ignore="RtlSymmetry" />
        <TextView
            android:visibility="gone"
            style="@style/OTPTextLineStyle"
            android:layout_marginStart="@dimen/delete_account_otp_line_error_margin_start"
            app:layout_constraintBottom_toTopOf="@+id/tv_resend_des"
            app:layout_constraintTop_toBottomOf="@id/edt_input_otp"
            app:layout_constraintStart_toStartOf="parent"
            android:id="@+id/underline_error"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="@string/delete_account_text_error_otp"
            />
        <TextView
            android:id="@+id/tv_resend_des"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_not_have_otp"
            style="@style/OTPTextStyle"
            android:textColor="@color/app_content_text_disable_color"
            android:layout_marginTop="@dimen/delete_account_otp_text_resend_margin_top"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toBottomOf="@id/edt_input_otp"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:layout_marginStart="@dimen/delete_account_otp_text_resend_margin_start"
            android:id="@+id/tv_resend"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_resend_otp"
            style="@style/OTPTextStyle"
            android:textColor="@color/accent"
            android:layout_marginTop="@dimen/delete_account_otp_text_resend_margin_top"
            app:layout_constraintTop_toBottomOf="@id/edt_input_otp"
            app:layout_constraintStart_toEndOf="@id/tv_resend_des"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>