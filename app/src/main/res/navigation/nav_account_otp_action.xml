<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:startDestination="@id/starting_account_otp_fragment"
    android:id="@+id/nav_account_otp_action">
    <fragment
        android:id="@+id/starting_account_otp_fragment"
        android:name="com.fptplay.mobile.features.mega.account.StartingAccountOtpFragment"
        android:label="StartingAccountOtpFragment"
        tools:layout="@layout/starting_fragment">
        <argument
            android:name="isPackage"
            android:defaultValue="false"
            app:argType="boolean" />

        <argument
            android:name="verifyToken"
            app:argType="string"
            android:defaultValue=""
            />
        <argument
            android:name="email"
            app:argType="string"
            android:defaultValue="" />
        <argument
            android:name="targetScreen"
            app:argType="com.fptplay.mobile.features.mega.account.model.AccountOtpTargetScreen" />
    </fragment>

    <fragment
        android:id="@+id/delete_account_package_fragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountDeletePackageUserFragment"
        android:label="AccountDeletePackageUserFragment"
        tools:layout="@layout/account_delete_package_user_fragment">
    </fragment>

    <fragment
        android:id="@+id/delete_account_policy_fragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountDeletePolicyFragment"
        android:label="AccountDeletePolicyFragment"
        tools:layout="@layout/account_delete_policy_fragment">
    </fragment>
    <fragment
        android:id="@+id/account_verify_otp_fragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountVerifyOtpFragment"
        android:label="AccountVerifyOtpFragment"
        tools:layout="@layout/account_verify_otp_fragment">
        <argument
            android:name="otpType"
            app:argType="com.fptplay.mobile.features.mega.account.model.AccountOtpType" />

        <argument
            android:name="verifyToken"
            app:argType="string"
            />

        <argument
            android:name="email"
            app:argType="string"
            android:defaultValue="" />
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popupToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />
    </fragment>
    <action
        android:id="@+id/action_global_to_delete_account_package_user_fragment"
        app:destination="@id/delete_account_package_fragment"/>

    <action
        android:id="@+id/action_global_to_delete_account_policy_fragment"
        app:destination="@id/delete_account_policy_fragment"/>

    <action
        android:id="@+id/action_global_account_to_verity_account_otp_v2_fragment"
        app:destination="@id/account_verify_otp_fragment"/>
    <fragment
        android:id="@+id/accountChangePinSettingFragment"
        android:name="com.fptplay.mobile.features.mega.account.AccountChangePinSettingFragment"
        android:label="AccountChangePinSettingFragment"
        tools:layout="@layout/account_change_pin_setting_fragment">
        <argument android:name="createPinSetting"
            app:argType="boolean"/>
        <argument android:name="verifyToken"
            app:argType="string"/>
        <argument
            android:name="popupToId"
            android:defaultValue="-1"
            app:argType="integer" />
        <argument
            android:name="popupToInclusive"
            android:defaultValue="true"
            app:argType="boolean" />

    </fragment>

    <action
        android:id="@+id/action_global_to_account_change_pin_setting_fragment"
        app:destination="@id/accountChangePinSettingFragment"/>

</navigation>