{"formatVersion": 1, "database": {"version": 9, "identityHash": "079e772cf3230a746d0d3d5b78573744", "entities": [{"tableName": "VideoCollectionEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`movie_id` TEXT NOT NULL, `movie_name` TEXT NOT NULL, `movie_des` TEXT NOT NULL, `img_url` TEXT NOT NULL, `is_series` INTEGER NOT NULL, `real_episode_total` INTEGER NOT NULL DEFAULT -1, `id` INTEGER PRIMARY KEY AUTOINCREMENT, `modify_date` INTEGER NOT NULL DEFAULT CURRENT_TIMESTAMP, `create_time` INTEGER NOT NULL DEFAULT 0)", "fields": [{"fieldPath": "movieId", "columnName": "movie_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "movieName", "columnName": "movie_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "movieDescription", "columnName": "movie_des", "affinity": "TEXT", "notNull": true}, {"fieldPath": "imgUrl", "columnName": "img_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isSeries", "columnName": "is_series", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "realEpisodeTotal", "columnName": "real_episode_total", "affinity": "INTEGER", "notNull": true, "defaultValue": "-1"}, {"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "modifyDate", "columnName": "modify_date", "affinity": "INTEGER", "notNull": true, "defaultValue": "CURRENT_TIMESTAMP"}, {"fieldPath": "createTime", "columnName": "create_time", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "VideoChapterEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`chapter_id` TEXT NOT NULL, `download_url` TEXT NOT NULL, `thumb_url` TEXT NOT NULL, `chapter_name` TEXT NOT NULL, `chapter_index` INTEGER NOT NULL, `duration` INTEGER NOT NULL, `total_size` INTEGER NOT NULL, `modify_date` INTEGER NOT NULL DEFAULT CURRENT_TIMESTAMP, `id_chap` INTEGER PRIMARY KEY AUTOINCREMENT, `collection_id` INTEGER, `episode_id` TEXT NOT NULL DEFAULT '', `download_progress` REAL NOT NULL, `download_stage` INTEGER NOT NULL, `saved_size` INTEGER NOT NULL, `file_id` TEXT NOT NULL, `file_path` TEXT NOT NULL, `file_name` TEXT NOT NULL, `is_outdated` INTEGER NOT NULL DEFAULT 0, `is_airline` INTEGER NOT NULL DEFAULT 1, `profile_id` TEXT NOT NULL, `expired_time` INTEGER NOT NULL, `time_start_intro` INTEGER NOT NULL, `time_start_content` INTEGER NOT NULL, `time_end_content` INTEGER NOT NULL, `overlay_logo` TEXT NOT NULL, `maturity_advisories` TEXT NOT NULL, `maturity_position` TEXT NOT NULL, `maturity_prefix` TEXT NOT NULL, `maturity_value` TEXT NOT NULL, `background_url` TEXT NOT NULL DEFAULT '', `play_mode` INTEGER NOT NULL DEFAULT 0, `stream_session` TEXT NOT NULL DEFAULT '', `is_drm` INTEGER NOT NULL DEFAULT 0, `key_off_id` TEXT NOT NULL DEFAULT '', `session` TEXT NOT NULL DEFAULT '', `merchant` TEXT NOT NULL DEFAULT '', `key_state` INTEGER NOT NULL DEFAULT 0, `create_time` INTEGER NOT NULL DEFAULT 0, FOREIGN KEY(`collection_id`) REFERENCES `VideoCollectionEntity`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "chapterId", "columnName": "chapter_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "download_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "thumbUrl", "columnName": "thumb_url", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "chapter_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "chapterIdx", "columnName": "chapter_index", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "duration", "columnName": "duration", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalSize", "columnName": "total_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "modifyDate", "columnName": "modify_date", "affinity": "INTEGER", "notNull": true, "defaultValue": "CURRENT_TIMESTAMP"}, {"fieldPath": "id", "columnName": "id_chap", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "collectionId", "columnName": "collection_id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "episodeId", "columnName": "episode_id", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "downloadProgress", "columnName": "download_progress", "affinity": "REAL", "notNull": true}, {"fieldPath": "downloadStage", "columnName": "download_stage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "savedSize", "columnName": "saved_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "fileId", "columnName": "file_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "filePath", "columnName": "file_path", "affinity": "TEXT", "notNull": true}, {"fieldPath": "fileName", "columnName": "file_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isOutdated", "columnName": "is_outdated", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isAirline", "columnName": "is_airline", "affinity": "INTEGER", "notNull": true, "defaultValue": "1"}, {"fieldPath": "profileId", "columnName": "profile_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "expiredTime", "columnName": "expired_time", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeStartIntro", "columnName": "time_start_intro", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeStartContent", "columnName": "time_start_content", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timeEndContent", "columnName": "time_end_content", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "overlayLogo", "columnName": "overlay_logo", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maturityAdvisories", "columnName": "maturity_advisories", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maturityPosition", "columnName": "maturity_position", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maturityPrefix", "columnName": "maturity_prefix", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maturityValue", "columnName": "maturity_value", "affinity": "TEXT", "notNull": true}, {"fieldPath": "backgroundUrl", "columnName": "background_url", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "playMode", "columnName": "play_mode", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "streamSession", "columnName": "stream_session", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "isDrm", "columnName": "is_drm", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "keyOffId", "columnName": "key_off_id", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "session", "columnName": "session", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "merchant", "columnName": "merchant", "affinity": "TEXT", "notNull": true, "defaultValue": "''"}, {"fieldPath": "keyState", "columnName": "key_state", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "createTime", "columnName": "create_time", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": true, "columnNames": ["id_chap"]}, "indices": [{"name": "index_VideoChapterEntity_collection_id", "unique": false, "columnNames": ["collection_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_VideoChapterEntity_collection_id` ON `${TABLE_NAME}` (`collection_id`)"}], "foreignKeys": [{"table": "VideoCollectionEntity", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["collection_id"], "referencedColumns": ["id"]}]}, {"tableName": "WarningScenarioEntity", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`content` TEXT NOT NULL, `from` INTEGER NOT NULL, `to` INTEGER NOT NULL, `id_warning` INTEGER PRIMARY KEY AUTOINCREMENT, `chapter_id` INTEGER, FOREIGN KEY(`chapter_id`) REFERENCES `VideoChapterEntity`(`id_chap`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "content", "columnName": "content", "affinity": "TEXT", "notNull": true}, {"fieldPath": "from", "columnName": "from", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "to", "columnName": "to", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "id", "columnName": "id_warning", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "chapterId", "columnName": "chapter_id", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id_warning"]}, "indices": [{"name": "index_WarningScenarioEntity_chapter_id", "unique": false, "columnNames": ["chapter_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_WarningScenarioEntity_chapter_id` ON `${TABLE_NAME}` (`chapter_id`)"}], "foreignKeys": [{"table": "VideoChapterEntity", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["chapter_id"], "referencedColumns": ["id_chap"]}]}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '079e772cf3230a746d0d3d5b78573744')"]}}